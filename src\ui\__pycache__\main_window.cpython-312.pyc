�

    ��h�`  �                   �   � d dl Z d dlmZmZmZmZmZmZmZm	Z	m
Z
mZmZm
Z
mZ d dlmZmZmZ d dlmZmZmZ d dlmZmZ d dlmZ d dlmZ  G d� d	e�      Zy)
�    N)
�QMainWindow�QWidget�QVBoxLayout�QHBoxLayout�QPushButton�QListWidget�QListWidgetItem�QLabel�QSlider�QFileDialog�QMessageBox�QColorDialog�	QGroupBox)�Qt�QSize�pyqtSlot)�QIcon�QPixmap�QColor)�Song�
SongLoader)�AudioPlayer)�KaraokeViewc                   �   � � e Zd ZdZ� fd�Zd� Zd� Zd� Zd� Zd� Z	d� Z
d	� Zd
� Zd� Z
d� Zd
� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Zd� Z� xZS )�
MainWindowu#   Ventana principal de la aplicaciónc                 ��   �� t         �| �  �        | j                  d�       | j                  dd�       t	        �       | _        g | _        d | _        d| _        | j                  �        | j                  �        y )NzUltraStar Simplei   iX  g      �?)�super�__init__�setWindowTitle�setMinimumSizer   �audio_player�songs�current_song�smoothing_factor�	_setup_ui�_connect_signals)�self�	__class__s    ��?C:\Users\<USER>\Desktop\ultrastar_basics\src\ui\main_window.pyr   zMainWindow.__init__   sn   �� �
���� 	
���.�/����C��%� (�M�����
� ��� !$��� 	
���� 	
����    c                 �  � � t        �       }� j                  |�       t        |�      }t        �       }t	        �       � _        � j
                  j
                  d�       |j                  � j
                  �       t        �       }t        d�      � _	        |j                  � j                  �       t        �       � _
        � j                  j                  dd�       � j                  j                  t        j                  �       � j                  j                  d�       |j                  � j                  �       t        �       }t!        d�      � _        t!        d�      � _        t!        d�      � _        |j                  � j"                  �       |j                  � j$                  �       |j                  � j&                  �       |j)                  |�       t+        t        j,                  �      � _        |j                  � j.                  �       t        d�      � _        � j0                  j                  t        j                  �       |j                  � j0                  �       t!        d	�      � _        |j                  � j2                  �       t5        d
�      }t        �       }t        �       }t!        d�      � _        t!        d�      � _        |j                  � j6                  �       |j                  � j8                  �       t        �       }	t!        d
�      � _        � j:                  j=                  d�       � j:                  j?                  d�       |	j                  � j:                  �       t        d�      � _         |	j                  � j@                  �       t+        t        j,                  �      � _!        � jB                  jE                  d�       � jB                  jG                  d�       � jB                  jI                  d�       � jB                  jK                  t*        jL                  �       � jB                  jO                  d�       |	j                  � jB                  �       t        �       }
t        d�      � _(        |
j                  � jP                  �       t+        t        j,                  �      � _)        � jR                  jE                  d�       � jR                  jG                  d�       � jR                  jI                  d�       � jR                  jK                  t*        jL                  �       � jR                  jO                  d�       |
j                  � jR                  �       t        �       }t        d�      � _*        |j                  � jT                  �       t+        t        j,                  �      � _+        � jV                  jE                  d�       � jV                  jG                  d�       � jV                  jI                  d�       � jV                  jK                  t*        jL                  �       � jV                  jO                  d�       |j                  � jV                  �       t        �       }t        d�      � _,        |j                  � jX                  �       t+        t        j,                  �      � _-        � jZ                  jE                  d�       � jZ                  jG                  d�       � jZ                  jI                  d�       � jZ                  jK                  t*        jL                  �       � jZ                  jO                  d�       |j                  � jZ                  �       t!        d�      � _.        |j                  � j\                  �       t        �       }
t        d�      � _/        |
j                  � j^                  �       g � _0        t!        d�      � _1        � jb                  j=                  d�       � jb                  j?                  d�       � jb                  jd                  jg                  � fd ��       |
j                  � jb                  �       � j`                  ji                  � jb                  �       t!        d!�      � _5        � jj                  j=                  d�       � jj                  jd                  jg                  � fd"��       |
j                  � jj                  �       � j`                  ji                  � jj                  �       t!        d#�      � _6        � jl                  j=                  d�       � jl                  jd                  jg                  � fd$��       |
j                  � jl                  �       � j`                  ji                  � jl                  �       t!        d%�      � _7        � jn                  j=                  d�       � jn                  jd                  jg                  � fd&��       |
j                  � jn                  �       � j`                  ji                  � jn                  �       t!        d'�      � _8        � jp                  j=                  d�       � jp                  jd                  jg                  � fd(��       |
j                  � jp                  �       � j`                  ji                  � jp                  �       t!        d)�      � _9        � jr                  j=                  d�       � jr                  jd                  jg                  � fd*��       |
j                  � jr                  �       � j`                  ji                  � jr                  �       t!        d+�      � _:        � jt                  j=                  d�       � jt                  jd                  jg                  � fd,��       |
j                  � jt                  �       � j`                  ji                  � jt                  �       t!        d-�      � _;        � jv                  j=                  d�       � jv                  jd                  jg                  � fd.��       |
j                  � jv                  �       � j`                  ji                  � jv                  �       t!        d/�      � _<        � jx                  j=                  d�       � jx                  jd                  jg                  � fd0��       |
j                  � jx                  �       � j`                  ji                  � jx                  �       |j)                  |
�       |j)                  |�       |j)                  |�       |j)                  |
�       t!        d1�      � _=        � jz                  j=                  d�       � jz                  j?                  d�       |	j                  � jz                  �       |j)                  |�       |j)                  |	�       |j}                  |�       |j                  |�       |j)                  |�       |j)                  |�       t        �       � _@        |j                  � j�                  �       y2)3z Configura la interfaz de usuarioi,  u   No hay canción seleccionada��   zbackground-color: #333;�
Reproducir�Pausar�Detener�
00:00 / 00:00zCargar carpeta de cancioneszOpciones de KaraokezColor texto cantadozColor texto pendiente�   Efecto de transición: ONTu   Velocidad de transición: 6�   �
   �   u   Sincronización:i�����d   r   �   z
Suavizado:�2   u   Anticipación: 22ms�   �   �   Información detalladazEfecto de karaoke:�Simplec                  �&   �� � j                  d�      S )Nr   ��change_karaoke_effect�r'   s   �r)   �<lambda>z&MainWindow._setup_ui.<locals>.<lambda>�   s   �� �$�:T�:T�UV�:Wr*   �Zoomc                  �&   �� � j                  d�      S )Nr2   r=   r?   s   �r)   r@   z&MainWindow._setup_ui.<locals>.<lambda>�   �   �� ��8R�8R�ST�8Ur*   �Particlec                  �&   �� � j                  d�      S )N�   r=   r?   s   �r)   r@   z&MainWindow._setup_ui.<locals>.<lambda>�   s   �� �D�<V�<V�WX�<Yr*   �Ballc                  �&   �� � j                  d�      S )N�   r=   r?   s   �r)   r@   z&MainWindow._setup_ui.<locals>.<lambda>�   rC   r*   �Shiftc                  �&   �� � j                  d�      S )N�   r=   r?   s   �r)   r@   z&MainWindow._setup_ui.<locals>.<lambda>�   �   �� ��9S�9S�TU�9Vr*   �Wavec                  �&   �� � j                  d�      S )Nr9   r=   r?   s   �r)   r@   z&MainWindow._setup_ui.<locals>.<lambda>�   rC   r*   �Pulsec                  �&   �� � j                  d�      S )Nr4   r=   r?   s   �r)   r@   z&MainWindow._setup_ui.<locals>.<lambda>�   rM   r*   �
Typewriterc                  �&   �� � j                  d�      S )N�   r=   r?   s   �r)   r@   z&MainWindow._setup_ui.<locals>.<lambda>�   s   �� �d�>X�>X�YZ�>[r*   z
Aegisub 3Dc                  �&   �� � j                  d�      S )N�   r=   r?   s   �r)   r@   z&MainWindow._setup_ui.<locals>.<lambda>�   s   �� �4�;U�;U�VW�;Xr*   �   Interpolación: ONN)Ar   �setCentralWidgetr   r   r   �	song_list�setMinimumWidth�	addWidgetr
   �	song_info�cover_label�setFixedSize�setAlignmentr   �AlignCenter�
setStyleSheetr   �play_button�pause_button�stop_button�	addLayoutr   �
Horizontal�progress_slider�
time_label�load_buttonr   �sung_color_button�pending_color_button�transition_effect_button�setCheckable�
setChecked�transition_speed_label�transition_speed_slider�
setMinimum�
setMaximum�setValue�setTickPosition�
TicksBelow�setTickInterval�
sync_label�sync_slider�smoothing_label�smoothing_slider�anticipation_label�anticipation_slider�debug_button�effect_label�effect_buttons�effect_simple_button�clicked�connect�append�effect_zoom_button�effect_particle_button�effect_ball_button�effect_shift_button�effect_wave_button�effect_pulse_button�effect_typewriter_button�effect_aegisub_button�interpolation_button�	setLayoutr   �karaoke_view)r'   �central_widget�main_layout�
top_layout�info_layout�controls_layout�karaoke_options�karaoke_layout�color_layout�options_layout�options_layout2�options_layout3�options_layout4�options_layout5s   `             r)   r%   zMainWindow._setup_ui(   s�
  �� � !������n�-� "�.�1�� !�]�
� %�������&�&�s�+����T�^�^�,� "�m��  � >�?������d�n�n�-� "�8������%�%�c�3�/����%�%�b�n�n�5����&�&�'@�A����d�.�.�/� &�-��&�|�4���'��1���&�y�1����!�!�$�"2�"2�3��!�!�$�"3�"3�4��!�!�$�"2�"2�3����o�.�  '�r�}�}�5������d�2�2�3� !��1������$�$�R�^�^�4����d�o�o�.� '�'D�E������d�.�.�/� $�$9�:��$��� #�}��!,�-B�!C���$/�0G�$H��!����t�5�5�6����t�8�8�9� %���(3�4O�(P��%��%�%�2�2�4�8��%�%�0�0��6�� � ��!>�!>�?� '-�-J�&K��#�� � ��!<�!<�=�'.�r�}�}�'=��$��$�$�/�/��2��$�$�/�/��3��$�$�-�-�a�0��$�$�4�4�W�5G�5G�H��$�$�4�4�Q�7�� � ��!=�!=�>� &�-�� !�!3�4����!�!�$�/�/�2�"�2�=�=�1������#�#�D�)����#�#�C�(����!�!�!�$����(�(��);�);�<����(�(��,��!�!�$�"2�"2�3� &�-��  &�l�3����!�!�$�"6�"6�7� '��
�
� 6������(�(��+����(�(��-����&�&�r�*����-�-�g�.@�.@�A����-�-�b�1��!�!�$�"7�"7�8� &�-�� #)�)>�"?����!�!�$�"9�"9�:�#*�2�=�=�#9�� �� � �+�+�A�.�� � �+�+�B�/�� � �)�)�"�-�� � �0�0��1C�1C�D�� � �0�0��3��!�!�$�":�":�;� (�(@�A����!�!�$�"3�"3�4� &�-�� #�#7�8����!�!�$�"3�"3�4� !��� %0��$9��!��!�!�.�.�t�4��!�!�,�,�T�2��!�!�)�)�1�1�2W�X��!�!�$�";�";�<����"�"�4�#<�#<�=� #.�f�"5������,�,�T�2����'�'�/�/�0U�V��!�!�$�"9�"9�:����"�"�4�#:�#:�;� '2�*�&=��#��#�#�0�0��6��#�#�+�+�3�3�4Y�Z��!�!�$�"=�"=�>����"�"�4�#>�#>�?� #.�f�"5������,�,�T�2����'�'�/�/�0U�V��!�!�$�"9�"9�:����"�"�4�#:�#:�;� $/�w�#7�� �� � �-�-�d�3�� � �(�(�0�0�1V�W��!�!�$�":�":�;����"�"�4�#;�#;�<� #.�f�"5������,�,�T�2����'�'�/�/�0U�V��!�!�$�"9�"9�:����"�"�4�#:�#:�;� $/�w�#7�� �� � �-�-�d�3�� � �(�(�0�0�1V�W��!�!�$�":�":�;����"�"�4�#;�#;�<� )4�L�(A��%��%�%�2�2�4�8��%�%�-�-�5�5�6[�\��!�!�$�"?�"?�@����"�"�4�#@�#@�A� &1��%>��"��"�"�/�/��5��"�"�*�*�2�2�3X�Y��!�!�$�"<�"<�=����"�"�4�#=�#=�>�
 	� � ��1� 	� � ��1� 	� � ��1� 	� � ��1�$/�0D�$E��!��!�!�.�.�t�4��!�!�,�,�T�2�� � ��!:�!:�;�� � ��.�� � ��0��!�!�.�1����o�.����[�)� 	���j�)� (�M������d�/�/�0r*   c                 �B  � | j                   j                  j                  | j                  �       | j                  j                  j                  | j
                  �       | j                  j                  j                  | j                  �       | j                  j                  j                  | j                  �       | j                  j                  j                  | j                  �       | j                  j                  j                  | j                  �       | j                  j                  j                  | j                  �       | j                   j                  j                  | j"                  �       | j$                  j&                  j                  | j(                  �       | j*                  j&                  j                  | j,                  �       | j.                  j&                  j                  | j0                  �       | j2                  j&                  j                  | j4                  �       | j6                  j                  j                  | j8                  �       | j:                  j<                  j                  | j>                  �       | j@                  jB                  j                  | jD                  �       | j@                  jF                  j                  | jH                  �       | jJ                  jL                  j                  | jN                  �       y)u'   Conecta las señales de los componentesN)(rb   r�   r�   �playrc   �pauserd   �stopri   �load_songs_folderrj   �change_sung_colorrk   �change_pending_colorrl   �toggle_transition_effectr�   �toggle_interpolationrp   �valueChanged�change_transition_speedrx   �change_sync_offsetrz   �change_smoothing_factorr|   �change_anticipationr}   �show_debug_inforY   �itemClicked�
song_selectedr!   �positionChanged�update_position�durationChanged�update_durationrg   �sliderMoved�seekr?   s    r)   r&   zMainWindow._connect_signals  s,  � � 	
��� � �(�(����3����!�!�)�)�$�*�*�5���� � �(�(����3���� � �(�(��)?�)?�@� 	
���&�&�.�.�t�/E�/E�F��!�!�)�)�1�1�$�2K�2K�L��%�%�-�-�5�5�d�6S�6S�T��!�!�)�)�1�1�$�2K�2K�L� 	
�$�$�1�1�9�9�$�:V�:V�W� 	
���%�%�-�-�d�.E�.E�F� 	
���*�*�2�2�4�3O�3O�P� 	
� � �-�-�5�5�d�6N�6N�O� 	
���!�!�)�)�$�*>�*>�?� 	
���"�"�*�*�4�+=�+=�>� 	
���)�)�1�1�$�2F�2F�G����)�)�1�1�$�2F�2F�G� 	
���(�(�0�0����;r*   c                 �X   � t        j                  | d�      }|r| j                  |�       yy)z!Carga canciones desde una carpetaz Seleccionar carpeta de cancionesN)r   �getExistingDirectory�
load_songs�r'   �folders     r)   r�   zMainWindow.load_songs_folderB  s)   � ��1�1�$�8Z�[����O�O�F�#� r*   c                 �X   � t        j                  |�      | _        | j                  �        y)z-Carga canciones desde la carpeta especificadaN)r   �load_songs_from_directoryr"   �update_song_listr�   s     r)   r�   zMainWindow.load_songsH  s    � ��9�9�&�A��
����r*   c                 �  � | j                   j                  �        | j                  D ]a  }t        |j                  � d|j
                  � ��      }|j
                  t        j                  |�       | j                   j                  |�       �c y)z.Actualiza la lista de canciones en la interfaz� - N)
rY   �clearr"   r	   �artist�title�setDatar   �UserRole�addItem)r'   �song�items      r)   r�   zMainWindow.update_song_listM  sa   � ��������J�J�D�"�d�k�k�]�#�d�j�j�\�#B�C�D��L�L����d�+��N�N�"�"�4�(� r*   c                 �d   � |j                  t        j                  �      }| j                  |�       y)u0   Manejador para cuando se selecciona una canciónN)�datar   r�   �	load_song)r'   r�   r�   s      r)   r�   zMainWindow.song_selectedV  s    � ��y�y����%�����t�r*   c                 �V  � || _         | j                  �        |j                  �       }| j                  j	                  |j
                  � d|j                  � d|j                  dz  d�d|j                  � d|d�d|j                  � d|j                  � d	t        |j                  �      � ��       |j                  �       }|rtt        j                  j!                  |�      rUt#        |�      }| j$                  j'                  |j)                  d
d
t*        j,                  t*        j.                  �      �       n>| j$                  j	                  d�       | j$                  j'                  t#        �       �       | j0                  j3                  d�       | j4                  j	                  d
�       |j7                  �       }t        j                  j!                  |�      r9| j8                  j;                  |�       d}| j<                  j?                  |�       ytA        jB                  | dd|� ��       y)u   Carga una canciónr�   z
BPM: rL   �.2fz, GAP: � ms (u
    s)
Género: u   , Año: u
   
Líneas: r,   zSin portadar   r0   u�  
# Modificar el factor de suavizado en song_loader.py
import types

def get_current_line_and_note_patched(self, current_time):
    # Llamar al método original
    line, note, progress = original_method(self, current_time)
    return line, note, progress

# Guardar el método original
original_method = song.get_current_line_and_note

# Reemplazar con nuestra versión
song.get_current_line_and_note = types.MethodType(get_current_line_and_note_patched, song)
�Errorz*No se pudo encontrar el archivo de audio: N)"r#   r�   �get_gap_secondsr\   �setTextr�   r�   �bpm�gap�genre�year�len�lines�get_full_cover_path�os�path�existsr   r]   �	setPixmap�scaledr   �KeepAspectRatio�SmoothTransformationrg   rs   rh   �get_full_mp3_pathr!   �loadr�   �set_songr
   �warning)r'   r�   �gap_seconds�
cover_path�pixmap�mp3_path�codes          r)   r�   zMainWindow.load_song[  s�  � � ��� 	
�	�	�� �*�*�,�������$�+�+��c�$�*�*�� >&�&*�h�h�q�j��%5�W�T�X�X�J�e�K�X[�K\� ]*�*.�*�*��X�d�i�i�[� I*�*-�d�j�j�/�):� <� 	=� �-�-�/�
��"�'�'�.�.��4��Z�(�F����&�&�v�}�}�S�#�r�?Q�?Q�SU�Sj�Sj�'k�l����$�$�]�3����&�&�w�y�1� 	
���%�%�a�(�������0� �)�)�+��
�7�7�>�>�(�#����"�"�8�,�
�D�& 
���&�&�t�,�����g�1[�\d�[e�/f�gr*   c                 �R   � | j                   r| j                  j                  �        yy)u   Reproduce la canción actualN)r#   r!   r�   r?   s    r)   r�   zMainWindow.play�  s"   � �������"�"�$� r*   c                 �8   � | j                   j                  �        y)u   Pausa la reproducciónN)r!   r�   r?   s    r)   r�   zMainWindow.pause�  s   � ������!r*   c                 �n   � | j                   j                  �        | j                   j                  d�       y)u   Detiene la reproducciónr   N)r!   r�   �set_positionr?   s    r)   r�   zMainWindow.stop�  s(   � ������ ����&�&�q�)r*   c                 �   � | j                   j                  �       dkD  r!|dz  }| j                   j                  |�       yy)u$   Cambia la posición de reproducciónr   �     @�@N)r!   �get_durationr�   )r'   �position�relative_positions      r)   r�   zMainWindow.seek�  s>   � ����)�)�+�a�/� (�6� 1�����*�*�+<�=� 0r*   c                 �  � | j                   j                  �       dkD  rDt        || j                   j                  �       z  dz  �      }| j                  j	                  |�       | j                   j                  �       }| j
                  j
                  | j                  |�      � d| j                  |�      � ��       | j                  j                  |�       y)u   Actualiza la posición actualr   i�  � / N)
r!   r�   �intrg   rs   rh   r�   �_format_timer�   �update_time)r'   r�   r�   �durations       r)   r�   zMainWindow.update_position�  s�   � � ���)�)�+�a�/� #�H�t�/@�/@�/M�/M�/O�$O�RV�$V� W��� � �)�)�*;�<� �$�$�1�1�3�������4�#4�#4�X�#>�"?�s�4�CT�CT�U]�C^�B_� `�a� 	
���%�%�h�/r*   c                 �   � | j                   j                  �       }| j                  j                  | j	                  |�      � d| j	                  |�      � ��       y)u   Actualiza la duración totalr�   N)r!   �get_positionrh   r�   r�   )r'   r�   r�   s      r)   r�   zMainWindow.update_duration�  sN   � � �$�$�1�1�3�������4�#4�#4�X�#>�"?�s�4�CT�CT�U]�C^�B_� `�ar*   c                 �L   � t        |dz  �      }t        |dz  �      }|d�d|d��S )z&Formatea un tiempo en segundos a MM:SS�<   �02d�:)r�   )r'   �seconds�minutess      r)   r�   zMainWindow._format_time�  s3   � ��g��m�$���g��l�#���#��a���}�-�-r*   c                 ��   � | j                   j                  }t        j                  || d�      }|j	                  �       r,|| j                   _        | j                   j                  �        yy)z!Cambia el color del texto cantadoz$Seleccionar color para texto cantadoN)r�   �
color_sungr   �getColor�isValid�update�r'   �
current_color�colors      r)   r�   zMainWindow.change_sung_color�  sV   � ��)�)�4�4�
��%�%�m�T�;a�b���=�=�?�+0�D���(����$�$�&� r*   c                 ��   � | j                   j                  }t        j                  || d�      }|j	                  �       r,|| j                   _        | j                   j                  �        yy)z#Cambia el color del texto pendientez&Seleccionar color para texto pendienteN)r�   �
color_pendingr   r�   r�   r   r  s      r)   r�   zMainWindow.change_pending_color�  sV   � ��)�)�7�7�
��%�%�m�T�;c�d���=�=�?�.3�D���+����$�$�&� r*   c                 �$  � | j                   j                  �       | j                  _        | j                  j                  r| j                   j	                  d�       n| j                   j	                  d�       | j                  j                  �        y)u/   Activa/desactiva el efecto de transición suaver1   u   Efecto de transición: OFFN)rl   �	isCheckedr�   �show_transition_effectr�   r   r?   s    r)   r�   z#MainWindow.toggle_transition_effect�  sl   � �37�3P�3P�3Z�3Z�3\����0� ���3�3��)�)�1�1�2M�N��)�)�1�1�2N�O���� � �"r*   c                 �^  � | j                   j                  �       }|r=t        | j                  j	                  �       �      | j
                  j                  _        nd| j
                  j                  _        |r| j                   j                  d�       y| j                   j                  d�       y)u/   Activa/desactiva la interpolación de posición�      Y@rW   u   Interpolación: OFFN)	r�   r  �floatrp   �valuer�   �engine�transition_speedr�   )r'   �interpolation_enableds     r)   r�   zMainWindow.toggle_interpolation�  s�   � � !%� 9� 9� C� C� E�� !�8=�d�>Z�>Z�>`�>`�>b�8c�D���$�$�5� 9>�D���$�$�5� !��%�%�-�-�.B�C��%�%�-�-�.C�Dr*   c                 �   � t        |�      }|| j                  j                  _        | j                  j                  d|� ��       y)u.   Cambia la velocidad de transición del karaokeu   Velocidad de transición: N)r  r�   r
  r  ro   r�   )r'   r  �speeds      r)   r�   z"MainWindow.change_transition_speed�  sA   � � �e��� 5:���� � �1� 	
�#�#�+�+�.H���,P�Qr*   c                 ��   � |}|dz  }|| j                   j                  _        |dkD  r | j                  j	                  d|� d��       y| j                  j	                  d|� d��       y)u/   Cambia el offset de sincronización del karaoker�   r   u   Sincronización: +�msu   Sincronización: N)r�   r
  �sync_offsetrw   r�   )r'   r  �	offset_ms�
offset_secs       r)   r�   zMainWindow.change_sync_offset   sj   � � �	���'�
� 0:���� � �,� �q�=��O�O�#�#�&8���2�$F�G��O�O�#�#�&7�	�{�"�$E�Fr*   c                 �Z   � |dz  }|| _         | j                  j                  d|� d��       y)z)Cambia el factor de suavizado del karaoker
  zSuavizado: �%N)r$   ry   r�   )r'   r  r$   s      r)   r�   z"MainWindow.change_smoothing_factor  s7   � � !�5�=�� !1��� 	
���$�$�{�5�'��%;�<r*   c                 �   � |}|dz  }|| j                   j                  _        | j                  j	                  d|� d��       y)u-   Cambia el tiempo de anticipación del karaoker�   u   Anticipación: r  N)r�   r
  �anticipation_timer{   r�   )r'   r  �anticipation_ms�anticipation_secs       r)   r�   zMainWindow.change_anticipation  sJ   � �  ��*�V�3�� 6F���� � �2� 	
���'�'�/�%���(C�Dr*   c                 �  � | j                   j                  |�       t        | j                  �      D ]  \  }}|j	                  ||k(  �       � | j                   j                  �       }t
        j                  | dd|� ��       y)zCambia el efecto de karaokezEfecto de KaraokezEfecto cambiado a: N)r�   �
set_effect�	enumerater   rn   �get_effect_namer
   �information)r'   �effect_type�i�button�effect_names        r)   r>   z MainWindow.change_karaoke_effect'  sy   � � 	
���$�$�[�1� #�4�#6�#6�7�I�A�v����a�;�.�/� 8� �'�'�7�7�9������&9�=P�Q\�P]�;^�_r*   c                 �H  � | j                   st        j                  | dd�       y| j                   }|j                  �       }d}|d|j                  � d�z
  }|d|j
                  � d�z
  }|d|j                  d	z  d
�d|j                  � d�z
  }|d
|j                  � d|d
�d�z
  }|d|j                  � d�z
  }|d|j                  � d�z
  }|d|j                  � d�z
  }|d|j                  �       � d�z
  }|d|j                  � d�z
  }|dt        |j                  �      � d�z
  }|d| j                  j!                  �       � d�z
  }|j                  �r;|j                  d   }|j                  d   }|d|j#                  �       � d�z
  }|d|j$                  � d|j'                  |j$                  �      d
�d�z
  }|d|j#                  �       � d�z
  }|d|j(                  � d|j'                  |j(                  �      d
�d�z
  }|j*                  r�|j*                  d   }|d|j,                  � d�z
  }|d|j$                  � d|j'                  |j$                  �      d
�d�z
  }|d |j.                  � d|j.                  d!z  |j                  z  d
�d�z
  }t        j                  | d"|�       y)#u4   Muestra información detallada de la canción actualu   Informaciónu   No hay ninguna canción cargadaNu(   Información detallada de la canción:

u	   Título: �
z	Artista: zBPM: rL   r�   z (valor interno: z)
zGAP: r�   z s)
u	   Género: u   Año: z
Archivo MP3: zRuta completa MP3: z	Portada: u   Número de líneas: zEfecto de karaoke actual: r   �����u   
Primera línea: u   Tiempo inicio primera línea: z beats (u   Última línea: u   Tiempo fin última línea: z
Primera nota: zTiempo inicio primera nota: u   Duración primera nota: r�   r:   )r#   r
   r!  r�   r�   r�   r�   r�   r�   r�   �mp3_filer�   �coverr�   r�   r�   r   �get_text�start�
get_beat_time�end�notes�text�length)r'   r�   r�   �info�
first_line�	last_line�
first_notes          r)   r�   zMainWindow.show_debug_info4  s  � �� � ��#�#�D�.�:[�\�� � � ���*�*�,��;���)�D�J�J�<�r�*�*���)�D�K�K�=��+�+���%�����
�3�'�'8����
�#�F�F���%����z��{�3�&7�u�=�=���)�D�J�J�<�r�*�*���&�����2�&�&���-��
�
��b�1�1���%�d�&<�&<�&>�%?�r�B�B���)�D�J�J�<�r�*�*���&�s�4�:�:��&7�r�:�:���,�T�->�->�-N�-N�-P�,Q�QS�T�T���:�:����A��J��
�
�2��I��(��)<�)<�)>�(?�r�B�B�D��4�Z�5E�5E�4F�h�t�Oa�Oa�bl�br�br�Os�tw�Nx�x}�~�~�D��&�y�'9�'9�';�&<�B�?�?�D��1�)�-�-����I[�I[�\e�\i�\i�Ij�kn�Ho�ot�u�u�D� ���'�-�-�a�0�
��*�:�?�?�*;�2�>�>���6�z�7G�7G�6H��QU�Qc�Qc�dn�dt�dt�Qu�vy�Pz�z�  A�  A���2�:�3D�3D�2E�X�j�N_�N_�bd�Nd�gk�go�go�No�ps�Mt�ty�z�z�� 	����&>��Er*   )�__name__�
__module__�__qualname__�__doc__r   r%   r&   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r>   r�   �
__classcell__)r(   s   @r)   r   r      s�   �� �-� �*q1�f%<�N$� �
)��
8h�t%�
"�*�
>�0�b�.�'�'�
#�E�(	R�
G�
=�
E�`�'Fr*   r   )r�   �PyQt5.QtWidgetsr   r   r   r   r   r   r	   r
   r   r   r
   r   r   �PyQt5.QtCorer   r   r   �PyQt5.QtGuir   r   r   �src.core.song_loaderr   r   �src.core.audio_playerr   �src.ui.karaoke_viewr   r   � r*   r)   �<module>rB     sI   �� 
�X� X� X� X� -� ,� .� .� 1� -� +�K	F�� K	Fr*   
