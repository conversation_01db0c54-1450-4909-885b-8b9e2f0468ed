�

    ��h�`  �                   �   � S SK r S SKJrJrJrJrJrJrJrJ	r	J
r
JrJrJ
r
Jr  S SKJrJrJr  S SKJrJrJr  S SKJrJr  S SKJr  S SKJr   " S S	\5      rg)
�    N)
�QMainWindow�QWidget�QVBoxLayout�QHBoxLayout�QPushButton�QListWidget�QListWidgetItem�QLabel�QSlider�QFileDialog�QMessageBox�QColorDialog�	QGroupBox)�Qt�QSize�pyqtSlot)�QIcon�QPixmap�QColor)�Song�
SongLoader)�AudioPlayer)�KaraokeViewc                   �   ^ � \ rS rSrSrU 4S jrS rS rS rS r	S r
S	 rS
 rS r
S rS
 rS rS rS rS rS rS rS rS rS rS rS rS rS rS rSrU =r$ )�
MainWindow�   u#   Ventana principal de la aplicaciónc                 ��   >� [         TU ]  5         U R                  S5        U R                  SS5        [	        5       U l        / U l        S U l        SU l        U R                  5         U R                  5         g )NzUltraStar Simplei   iX  g      �?)�super�__init__�setWindowTitle�setMinimumSizer   �audio_player�songs�current_song�smoothing_factor�	_setup_ui�_connect_signals)�self�	__class__s    ��[   C:\Users\<USER>\Desktop\Bacup\Widget_def_versión_1\ultrastar_basics\src\ui\main_window.pyr   �MainWindow.__init__   sn   �� �
���� 	
���.�/����C��%� (�M�����
� ��� !$��� 	
���� 	
����    c                 �  ^ � [        5       nT R                  U5        [        U5      n[        5       n[	        5       T l        T R
                  R
                  S5        UR                  T R
                  5        [        5       n[        S5      T l	        UR                  T R                  5        [        5       T l
        T R                  R                  SS5        T R                  R                  [        R                  5        T R                  R                  S5        UR                  T R                  5        [        5       n[!        S5      T l        [!        S5      T l        [!        S5      T l        UR                  T R"                  5        UR                  T R$                  5        UR                  T R&                  5        UR)                  U5        [+        [        R,                  5      T l        UR                  T R.                  5        [        S5      T l        T R0                  R                  [        R                  5        UR                  T R0                  5        [!        S	5      T l        UR                  T R2                  5        [5        S
5      n[        5       n[        5       n[!        S5      T l        [!        S5      T l        UR                  T R6                  5        UR                  T R8                  5        [        5       n	[!        S
5      T l        T R:                  R=                  S5        T R:                  R?                  S5        U	R                  T R:                  5        [        S5      T l         U	R                  T R@                  5        [+        [        R,                  5      T l!        T RB                  RE                  S5        T RB                  RG                  S5        T RB                  RI                  S5        T RB                  RK                  [*        RL                  5        T RB                  RO                  S5        U	R                  T RB                  5        [        5       n
[        S5      T l(        U
R                  T RP                  5        [+        [        R,                  5      T l)        T RR                  RE                  S5        T RR                  RG                  S5        T RR                  RI                  S5        T RR                  RK                  [*        RL                  5        T RR                  RO                  S5        U
R                  T RR                  5        [        5       n[        S5      T l*        UR                  T RT                  5        [+        [        R,                  5      T l+        T RV                  RE                  S5        T RV                  RG                  S5        T RV                  RI                  S5        T RV                  RK                  [*        RL                  5        T RV                  RO                  S5        UR                  T RV                  5        [        5       n[        S5      T l,        UR                  T RX                  5        [+        [        R,                  5      T l-        T RZ                  RE                  S5        T RZ                  RG                  S5        T RZ                  RI                  S5        T RZ                  RK                  [*        RL                  5        T RZ                  RO                  S5        UR                  T RZ                  5        [!        S5      T l.        UR                  T R\                  5        [        5       n
[        S5      T l/        U
R                  T R^                  5        / T l0        [!        S5      T l1        T Rb                  R=                  S5        T Rb                  R?                  S5        T Rb                  Rd                  Rg                  U 4S  j5        U
R                  T Rb                  5        T R`                  Ri                  T Rb                  5        [!        S!5      T l5        T Rj                  R=                  S5        T Rj                  Rd                  Rg                  U 4S" j5        U
R                  T Rj                  5        T R`                  Ri                  T Rj                  5        [!        S#5      T l6        T Rl                  R=                  S5        T Rl                  Rd                  Rg                  U 4S$ j5        U
R                  T Rl                  5        T R`                  Ri                  T Rl                  5        [!        S%5      T l7        T Rn                  R=                  S5        T Rn                  Rd                  Rg                  U 4S& j5        U
R                  T Rn                  5        T R`                  Ri                  T Rn                  5        [!        S'5      T l8        T Rp                  R=                  S5        T Rp                  Rd                  Rg                  U 4S( j5        U
R                  T Rp                  5        T R`                  Ri                  T Rp                  5        [!        S)5      T l9        T Rr                  R=                  S5        T Rr                  Rd                  Rg                  U 4S* j5        U
R                  T Rr                  5        T R`                  Ri                  T Rr                  5        [!        S+5      T l:        T Rt                  R=                  S5        T Rt                  Rd                  Rg                  U 4S, j5        U
R                  T Rt                  5        T R`                  Ri                  T Rt                  5        [!        S-5      T l;        T Rv                  R=                  S5        T Rv                  Rd                  Rg                  U 4S. j5        U
R                  T Rv                  5        T R`                  Ri                  T Rv                  5        [!        S/5      T l<        T Rx                  R=                  S5        T Rx                  Rd                  Rg                  U 4S0 j5        U
R                  T Rx                  5        T R`                  Ri                  T Rx                  5        UR)                  U
5        UR)                  U5        UR)                  U5        UR)                  U
5        [!        S15      T l=        T Rz                  R=                  S5        T Rz                  R?                  S5        U	R                  T Rz                  5        UR)                  U5        UR)                  U	5        UR}                  U5        UR                  U5        UR)                  U5        UR)                  U5        [        5       T l@        UR                  T R�                  5        g2)3z Configura la interfaz de usuarioi,  u   No hay canción seleccionada��   zbackground-color: #333;�
Reproducir�Pausar�Detener�
00:00 / 00:00zCargar carpeta de cancioneszOpciones de KaraokezColor texto cantadozColor texto pendiente�   Efecto de transición: ONTu   Velocidad de transición: 6�   �
   �   u   Sincronización:i�����d   r   �   z
Suavizado:�2   u   Anticipación: 22ms�   �   �   Información detalladazEfecto de karaoke:�Simplec                  �&   >� T R                  S5      $ )Nr   ��change_karaoke_effect�r(   s   �r*   �<lambda>�&MainWindow._setup_ui.<locals>.<lambda>�   s   �� �$�:T�:T�UV�:Wr,   �Zoomc                  �&   >� T R                  S5      $ )Nr4   r?   rA   s   �r*   rB   rC   �   �   �� ��8R�8R�ST�8Ur,   �Particlec                  �&   >� T R                  S5      $ )N�   r?   rA   s   �r*   rB   rC   �   s   �� �D�<V�<V�WX�<Yr,   �Ballc                  �&   >� T R                  S5      $ )N�   r?   rA   s   �r*   rB   rC   �   rF   r,   �Shiftc                  �&   >� T R                  S5      $ )N�   r?   rA   s   �r*   rB   rC   �   �   �� ��9S�9S�TU�9Vr,   �Wavec                  �&   >� T R                  S5      $ )Nr;   r?   rA   s   �r*   rB   rC   �   rF   r,   �Pulsec                  �&   >� T R                  S5      $ )Nr6   r?   rA   s   �r*   rB   rC   �   rP   r,   �
Typewriterc                  �&   >� T R                  S5      $ )N�   r?   rA   s   �r*   rB   rC   �   s   �� �d�>X�>X�YZ�>[r,   z
Aegisub 3Dc                  �&   >� T R                  S5      $ )N�   r?   rA   s   �r*   rB   rC   �   s   �� �4�;U�;U�VW�;Xr,   �   Interpolación: ONN)Ar   �setCentralWidgetr   r   r   �	song_list�setMinimumWidth�	addWidgetr
   �	song_info�cover_label�setFixedSize�setAlignmentr   �AlignCenter�
setStyleSheetr   �play_button�pause_button�stop_button�	addLayoutr   �
Horizontal�progress_slider�
time_label�load_buttonr   �sung_color_button�pending_color_button�transition_effect_button�setCheckable�
setChecked�transition_speed_label�transition_speed_slider�
setMinimum�
setMaximum�setValue�setTickPosition�
TicksBelow�setTickInterval�
sync_label�sync_slider�smoothing_label�smoothing_slider�anticipation_label�anticipation_slider�debug_button�effect_label�effect_buttons�effect_simple_button�clicked�connect�append�effect_zoom_button�effect_particle_button�effect_ball_button�effect_shift_button�effect_wave_button�effect_pulse_button�effect_typewriter_button�effect_aegisub_button�interpolation_button�	setLayoutr   �karaoke_view)r(   �central_widget�main_layout�
top_layout�info_layout�controls_layout�karaoke_options�karaoke_layout�color_layout�options_layout�options_layout2�options_layout3�options_layout4�options_layout5s   `             r*   r&   �MainWindow._setup_ui(   s�
  �� � !������n�-� "�.�1�� !�]�
� %�������&�&�s�+����T�^�^�,� "�m��  � >�?������d�n�n�-� "�8������%�%�c�3�/����%�%�b�n�n�5����&�&�'@�A����d�.�.�/� &�-��&�|�4���'��1���&�y�1����!�!�$�"2�"2�3��!�!�$�"3�"3�4��!�!�$�"2�"2�3����o�.�  '�r�}�}�5������d�2�2�3� !��1������$�$�R�^�^�4����d�o�o�.� '�'D�E������d�.�.�/� $�$9�:��$��� #�}��!,�-B�!C���$/�0G�$H��!����t�5�5�6����t�8�8�9� %���(3�4O�(P��%��%�%�2�2�4�8��%�%�0�0��6�� � ��!>�!>�?� '-�-J�&K��#�� � ��!<�!<�=�'.�r�}�}�'=��$��$�$�/�/��2��$�$�/�/��3��$�$�-�-�a�0��$�$�4�4�W�5G�5G�H��$�$�4�4�Q�7�� � ��!=�!=�>� &�-�� !�!3�4����!�!�$�/�/�2�"�2�=�=�1������#�#�D�)����#�#�C�(����!�!�!�$����(�(��);�);�<����(�(��,��!�!�$�"2�"2�3� &�-��  &�l�3����!�!�$�"6�"6�7� '��
�
� 6������(�(��+����(�(��-����&�&�r�*����-�-�g�.@�.@�A����-�-�b�1��!�!�$�"7�"7�8� &�-�� #)�)>�"?����!�!�$�"9�"9�:�#*�2�=�=�#9�� �� � �+�+�A�.�� � �+�+�B�/�� � �)�)�"�-�� � �0�0��1C�1C�D�� � �0�0��3��!�!�$�":�":�;� (�(@�A����!�!�$�"3�"3�4� &�-�� #�#7�8����!�!�$�"3�"3�4� !��� %0��$9��!��!�!�.�.�t�4��!�!�,�,�T�2��!�!�)�)�1�1�2W�X��!�!�$�";�";�<����"�"�4�#<�#<�=� #.�f�"5������,�,�T�2����'�'�/�/�0U�V��!�!�$�"9�"9�:����"�"�4�#:�#:�;� '2�*�&=��#��#�#�0�0��6��#�#�+�+�3�3�4Y�Z��!�!�$�"=�"=�>����"�"�4�#>�#>�?� #.�f�"5������,�,�T�2����'�'�/�/�0U�V��!�!�$�"9�"9�:����"�"�4�#:�#:�;� $/�w�#7�� �� � �-�-�d�3�� � �(�(�0�0�1V�W��!�!�$�":�":�;����"�"�4�#;�#;�<� #.�f�"5������,�,�T�2����'�'�/�/�0U�V��!�!�$�"9�"9�:����"�"�4�#:�#:�;� $/�w�#7�� �� � �-�-�d�3�� � �(�(�0�0�1V�W��!�!�$�":�":�;����"�"�4�#;�#;�<� )4�L�(A��%��%�%�2�2�4�8��%�%�-�-�5�5�6[�\��!�!�$�"?�"?�@����"�"�4�#@�#@�A� &1��%>��"��"�"�/�/��5��"�"�*�*�2�2�3X�Y��!�!�$�"<�"<�=����"�"�4�#=�#=�>�
 	� � ��1� 	� � ��1� 	� � ��1� 	� � ��1�$/�0D�$E��!��!�!�.�.�t�4��!�!�,�,�T�2�� � ��!:�!:�;�� � ��.�� � ��0��!�!�.�1����o�.����[�)� 	���j�)� (�M������d�/�/�0r,   c                 �B  � U R                   R                  R                  U R                  5        U R                  R                  R                  U R
                  5        U R                  R                  R                  U R                  5        U R                  R                  R                  U R                  5        U R                  R                  R                  U R                  5        U R                  R                  R                  U R                  5        U R                  R                  R                  U R                  5        U R                   R                  R                  U R"                  5        U R$                  R&                  R                  U R(                  5        U R*                  R&                  R                  U R,                  5        U R.                  R&                  R                  U R0                  5        U R2                  R&                  R                  U R4                  5        U R6                  R                  R                  U R8                  5        U R:                  R<                  R                  U R>                  5        U R@                  RB                  R                  U RD                  5        U R@                  RF                  R                  U RH                  5        U RJ                  RL                  R                  U RN                  5        g)u'   Conecta las señales de los componentesN)(re   r�   r�   �playrf   �pauserg   �stoprl   �load_songs_folderrm   �change_sung_colorrn   �change_pending_colorro   �toggle_transition_effectr�   �toggle_interpolationrs   �valueChanged�change_transition_speedr{   �change_sync_offsetr}   �change_smoothing_factorr   �change_anticipationr�   �show_debug_infor\   �itemClicked�
song_selectedr"   �positionChanged�update_position�durationChanged�update_durationrj   �sliderMoved�seekrA   s    r*   r'   �MainWindow._connect_signals  s,  � � 	
��� � �(�(����3����!�!�)�)�$�*�*�5���� � �(�(����3���� � �(�(��)?�)?�@� 	
���&�&�.�.�t�/E�/E�F��!�!�)�)�1�1�$�2K�2K�L��%�%�-�-�5�5�d�6S�6S�T��!�!�)�)�1�1�$�2K�2K�L� 	
�$�$�1�1�9�9�$�:V�:V�W� 	
���%�%�-�-�d�.E�.E�F� 	
���*�*�2�2�4�3O�3O�P� 	
� � �-�-�5�5�d�6N�6N�O� 	
���!�!�)�)�$�*>�*>�?� 	
���"�"�*�*�4�+=�+=�>� 	
���)�)�1�1�$�2F�2F�G����)�)�1�1�$�2F�2F�G� 	
���(�(�0�0����;r,   c                 �d   � [         R                  " U S5      nU(       a  U R                  U5        gg)z!Carga canciones desde una carpetaz Seleccionar carpeta de cancionesN)r   �getExistingDirectory�
load_songs�r(   �folders     r*   r�   �MainWindow.load_songs_folderB  s)   � ��1�1�$�8Z�[����O�O�F�#� r,   c                 �Z   � [         R                  " U5      U l        U R                  5         g)z-Carga canciones desde la carpeta especificadaN)r   �load_songs_from_directoryr#   �update_song_listr�   s     r*   r�   �MainWindow.load_songsH  s    � ��9�9�&�A��
����r,   c                 �  � U R                   R                  5         U R                   Hb  n[        UR                   SUR
                   35      nUR
                  [        R                  U5        U R                   R                  U5        Md     g)z.Actualiza la lista de canciones en la interfaz� - N)
r\   �clearr#   r	   �artist�title�setDatar   �UserRole�addItem)r(   �song�items      r*   r�   �MainWindow.update_song_listM  sa   � ��������J�J�D�"�d�k�k�]�#�d�j�j�\�#B�C�D��L�L����d�+��N�N�"�"�4�(� r,   c                 �d   � UR                  [        R                  5      nU R                  U5        g)u0   Manejador para cuando se selecciona una canciónN)�datar   r�   �	load_song)r(   r�   r�   s      r*   r�   �MainWindow.song_selectedV  s    � ��y�y����%�����t�r,   c                 �t  � Xl         U R                  5         UR                  5       nU R                  R	                  UR
                   SUR                   SUR                  S-  S SUR                   SUS SUR                   SUR                   S	[        UR                  5       35        UR                  5       nU(       ay  [        R                  R!                  U5      (       aU  [#        U5      nU R$                  R'                  UR)                  S
S
[*        R,                  [*        R.                  5      5        O>U R$                  R	                  S5        U R$                  R'                  [#        5       5        U R0                  R3                  S5        U R4                  R	                  S
5        UR7                  5       n[        R                  R!                  U5      (       a9  U R8                  R;                  U5        SnU R<                  R?                  U5        g[@        RB                  " U SSU 35        g)u   Carga una canciónr�   z
BPM: rO   �.2fz, GAP: � ms (u
    s)
Género: u   , Año: u
   
Líneas: r.   zSin portadar   r2   u�  
# Modificar el factor de suavizado en song_loader.py
import types

def get_current_line_and_note_patched(self, current_time):
    # Llamar al método original
    line, note, progress = original_method(self, current_time)
    return line, note, progress

# Guardar el método original
original_method = song.get_current_line_and_note

# Reemplazar con nuestra versión
song.get_current_line_and_note = types.MethodType(get_current_line_and_note_patched, song)
�Errorz*No se pudo encontrar el archivo de audio: N)"r$   r�   �get_gap_secondsr_   �setTextr�   r�   �bpm�gap�genre�year�len�lines�get_full_cover_path�os�path�existsr   r`   �	setPixmap�scaledr   �KeepAspectRatio�SmoothTransformationrj   rv   rk   �get_full_mp3_pathr"   �loadr�   �set_songr
   �warning)r(   r�   �gap_seconds�
cover_path�pixmap�mp3_path�codes          r*   r�   �MainWindow.load_song[  s�  � � �� 	
�	�	�� �*�*�,�������$�+�+��c�$�*�*�� >&�&*�h�h�q�j��%5�W�T�X�X�J�e�K�X[�K\� ]*�*.�*�*��X�d�i�i�[� I*�*-�d�j�j�/�):� <� 	=� �-�-�/�
��"�'�'�.�.��4�4��Z�(�F����&�&�v�}�}�S�#�r�?Q�?Q�SU�Sj�Sj�'k�l����$�$�]�3����&�&�w�y�1� 	
���%�%�a�(�������0� �)�)�+��
�7�7�>�>�(�#�#����"�"�8�,�
�D�& 
���&�&�t�,�����g�1[�\d�[e�/f�gr,   c                 �\   � U R                   (       a  U R                  R                  5         gg)u   Reproduce la canción actualN)r$   r"   r�   rA   s    r*   r�   �MainWindow.play�  s"   � �������"�"�$� r,   c                 �8   � U R                   R                  5         g)u   Pausa la reproducciónN)r"   r�   rA   s    r*   r�   �MainWindow.pause�  s   � ������!r,   c                 �n   � U R                   R                  5         U R                   R                  S5        g)u   Detiene la reproducciónr   N)r"   r�   �set_positionrA   s    r*   r�   �MainWindow.stop�  s(   � ������ ����&�&�q�)r,   c                 �   � U R                   R                  5       S:�  a!  US-  nU R                   R                  U5        gg)u$   Cambia la posición de reproducciónr   �     @�@N)r"   �get_durationr�   )r(   �position�relative_positions      r*   r�   �MainWindow.seek�  s>   � ����)�)�+�a�/� (�6� 1�����*�*�+<�=� 0r,   c                 �  � U R                   R                  5       S:�  aC  [        XR                   R                  5       -  S-  5      nU R                  R	                  U5        U R                   R                  5       nU R
                  R
                  U R                  U5       SU R                  U5       35        U R                  R                  U5        g)u   Actualiza la posición actualr   i�  � / N)
r"   r�   �intrj   rv   rk   r�   �_format_timer�   �update_time)r(   r�   r�   �durations       r*   r�   �MainWindow.update_position�  s�   � � ���)�)�+�a�/� #�H�/@�/@�/M�/M�/O�$O�RV�$V� W��� � �)�)�*;�<� �$�$�1�1�3�������4�#4�#4�X�#>�"?�s�4�CT�CT�U]�C^�B_� `�a� 	
���%�%�h�/r,   c                 �   � U R                   R                  5       nU R                  R                  U R	                  U5       SU R	                  U5       35        g)u   Actualiza la duración totalr�   N)r"   �get_positionrk   r�   r�   )r(   r  r�   s      r*   r�   �MainWindow.update_duration�  sN   � � �$�$�1�1�3�������4�#4�#4�X�#>�"?�s�4�CT�CT�U]�C^�B_� `�ar,   c                 �L   � [        US-  5      n[        US-  5      nUS SUS 3$ )z&Formatea un tiempo en segundos a MM:SS�<   �02d�:)r�   )r(   �seconds�minutess      r*   r�   �MainWindow._format_time�  s3   � ��g��m�$���g��l�#���#��a���}�-�-r,   c                 ��   � U R                   R                  n[        R                  " XS5      nUR	                  5       (       a+  X R                   l        U R                   R                  5         gg)z!Cambia el color del texto cantadoz$Seleccionar color para texto cantadoN)r�   �
color_sungr   �getColor�isValid�update�r(   �
current_color�colors      r*   r�   �MainWindow.change_sung_color�  sT   � ��)�)�4�4�
��%�%�m�;a�b���=�=�?�?�+0���(����$�$�&� r,   c                 ��   � U R                   R                  n[        R                  " XS5      nUR	                  5       (       a+  X R                   l        U R                   R                  5         gg)z#Cambia el color del texto pendientez&Seleccionar color para texto pendienteN)r�   �
color_pendingr   r  r  r  r  s      r*   r�   �MainWindow.change_pending_color�  sT   � ��)�)�7�7�
��%�%�m�;c�d���=�=�?�?�.3���+����$�$�&� r,   c                 �.  � U R                   R                  5       U R                  l        U R                  R                  (       a  U R                   R	                  S5        OU R                   R	                  S5        U R                  R                  5         g)u/   Activa/desactiva el efecto de transición suaver3   u   Efecto de transición: OFFN)ro   �	isCheckedr�   �show_transition_effectr�   r  rA   s    r*   r�   �#MainWindow.toggle_transition_effect�  sl   � �37�3P�3P�3Z�3Z�3\����0� ���3�3��)�)�1�1�2M�N��)�)�1�1�2N�O���� � �"r,   c                 �r  � U R                   R                  5       nU(       a=  [        U R                  R	                  5       5      U R
                  R                  l        OSU R
                  R                  l        U(       a  U R                   R                  S5        gU R                   R                  S5        g)u/   Activa/desactiva la interpolación de posición�      Y@rZ   u   Interpolación: OFFN)	r�   r  �floatrs   �valuer�   �engine�transition_speedr�   )r(   �interpolation_enableds     r*   r�   �MainWindow.toggle_interpolation�  s�   � � !%� 9� 9� C� C� E�� !�8=�d�>Z�>Z�>`�>`�>b�8c�D���$�$�5� 9>�D���$�$�5� !��%�%�-�-�.B�C��%�%�-�-�.C�Dr,   c                 �   � [        U5      nX R                  R                  l        U R                  R                  SU 35        g)u.   Cambia la velocidad de transición del karaokeu   Velocidad de transición: N)r  r�   r!  r"  rr   r�   )r(   r   �speeds      r*   r�   �"MainWindow.change_transition_speed�  s?   � � �e��� 5:��� � �1� 	
�#�#�+�+�.H���,P�Qr,   c                 ��   � UnUS-  nX0R                   R                  l        US:�  a   U R                  R	                  SU S35        gU R                  R	                  SU S35        g)u/   Cambia el offset de sincronización del karaoker�   r   u   Sincronización: +�msu   Sincronización: N)r�   r!  �sync_offsetrz   r�   )r(   r   �	offset_ms�
offset_secs       r*   r�   �MainWindow.change_sync_offset   sh   � � �	���'�
� 0:��� � �,� �q�=��O�O�#�#�&8���2�$F�G��O�O�#�#�&7�	�{�"�$E�Fr,   c                 �X   � US-  nX l         U R                  R                  SU S35        g)z)Cambia el factor de suavizado del karaoker  zSuavizado: �%N)r%   r|   r�   )r(   r   r%   s      r*   r�   �"MainWindow.change_smoothing_factor  s5   � � !�5�=�� !1�� 	
���$�$�{�5�'��%;�<r,   c                 �   � UnUS-  nX0R                   R                  l        U R                  R	                  SU S35        g)u-   Cambia el tiempo de anticipación del karaoker�   u   Anticipación: r)  N)r�   r!  �anticipation_timer~   r�   )r(   r   �anticipation_ms�anticipation_secs       r*   r�   �MainWindow.change_anticipation  sH   � �  ��*�V�3�� 6F��� � �2� 	
���'�'�/�%���(C�Dr,   c                 �  � U R                   R                  U5        [        U R                  5       H  u  p#UR	                  X!:H  5        M     U R                   R                  5       n[        R                  " U SSU 35        g)zCambia el efecto de karaokezEfecto de KaraokezEfecto cambiado a: N)r�   �
set_effect�	enumerater�   rq   �get_effect_namer
   �information)r(   �effect_type�i�button�effect_names        r*   r@   � MainWindow.change_karaoke_effect'  su   � � 	
���$�$�[�1� #�4�#6�#6�7�I�A����a�.�/� 8� �'�'�7�7�9������&9�=P�Q\�P]�;^�_r,   c                 �j  � U R                   (       d  [        R                  " U SS5        gU R                   nUR                  5       nSnUSUR                   S3-
  nUSUR
                   S3-
  nUSUR                  S	-  S
 SUR                   S3-
  nUS
UR                   SUS
 S3-
  nUSUR                   S3-
  nUSUR                   S3-
  nUSUR                   S3-
  nUSUR                  5        S3-
  nUSUR                   S3-
  nUS[        UR                  5       S3-
  nUSU R                  R!                  5        S3-
  nUR                  (       Ga@  UR                  S   nUR                  S   nUSUR#                  5        S3-
  nUSUR$                   SUR'                  UR$                  5      S
 S3-
  nUSUR#                  5        S3-
  nUSUR(                   SUR'                  UR(                  5      S
 S3-
  nUR*                  (       a�  UR*                  S   nUSUR,                   S3-
  nUSUR$                   SUR'                  UR$                  5      S
 S3-
  nUS UR.                   SUR.                  S!-  UR                  -  S
 S3-
  n[        R                  " U S"U5        g)#u4   Muestra información detallada de la canción actualu   Informaciónu   No hay ninguna canción cargadaNu(   Información detallada de la canción:

u	   Título: �
z	Artista: zBPM: rO   r�   z (valor interno: z)
zGAP: r�   z s)
u	   Género: u   Año: z
Archivo MP3: zRuta completa MP3: z	Portada: u   Número de líneas: zEfecto de karaoke actual: r   �����u   
Primera línea: u   Tiempo inicio primera línea: z beats (u   Última línea: u   Tiempo fin última línea: z
Primera nota: zTiempo inicio primera nota: u   Duración primera nota: r  r<   )r$   r
   r:  r�   r�   r�   r�   r�   r�   r�   �mp3_filer�   �coverr�   r�   r�   r9  �get_text�start�
get_beat_time�end�notes�text�length)r(   r�   r�   �info�
first_line�	last_line�
first_notes          r*   r�   �MainWindow.show_debug_info4  s  � �� � ��#�#�D�.�:[�\�� � � ���*�*�,��;���)�D�J�J�<�r�*�*���)�D�K�K�=��+�+���%�����
�3�'�'8����
�#�F�F���%����z��{�3�&7�u�=�=���)�D�J�J�<�r�*�*���&�����2�&�&���-��
�
��b�1�1���%�d�&<�&<�&>�%?�r�B�B���)�D�J�J�<�r�*�*���&�s�4�:�:��&7�r�:�:���,�T�->�->�-N�-N�-P�,Q�QS�T�T���:�:�:����A��J��
�
�2��I��(��)<�)<�)>�(?�r�B�B�D��4�Z�5E�5E�4F�h�t�Oa�Oa�bl�br�br�Os�tw�Nx�x}�~�~�D��&�y�'9�'9�';�&<�B�?�?�D��1�)�-�-����I[�I[�\e�\i�\i�Ij�kn�Ho�ot�u�u�D� ���'�-�-�a�0�
��*�:�?�?�*;�2�>�>���6�z�7G�7G�6H��QU�Qc�Qc�dn�dt�dt�Qu�vy�Pz�z�  A�  A���2�:�3D�3D�2E�X�j�N_�N_�bd�Nd�gk�go�go�No�ps�Mt�ty�z�z�� 	����&>��Er,   )&r~   r   r"   r`   r$   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   rl   rf   rn   re   rj   r%   r|   r}   r_   r\   r#   rg   rm   rz   r{   rk   ro   rr   rs   ) �__name__�
__module__�__qualname__�__firstlineno__�__doc__r   r&   r'   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r@   r�   �__static_attributes__�
__classcell__)r)   s   @r*   r   r      s�   �� �-� �*q1�f%<�N$� �
)��
8h�t%�
"�*�
>�0�b�.�'�'�
#�E�(	R�
G�
=�
E�`�'F� 'Fr,   r   )r�   �PyQt5.QtWidgetsr   r   r   r   r   r   r	   r
   r   r   r
   r   r   �PyQt5.QtCorer   r   r   �PyQt5.QtGuir   r   r   �src.core.song_loaderr   r   �src.core.audio_playerr   �src.ui.karaoke_viewr   r   � r,   r*   �<module>r_     sI   �� 
�X� X� X� X� -� ,� .� .� 1� -� +�K	F�� K	Fr,   
