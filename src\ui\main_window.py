#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QListWidget, QListWidgetItem, QLabel,
                            QSlider, QFileDialog, QMessageBox, QColorDialog, QGroupBox)
from PyQt5.QtCore import Qt, QSize, pyqtSlot
from PyQt5.QtGui import QIcon, QPixmap, QColor

from src.core.song_loader import Song, SongLoader
from src.core.audio_player import AudioPlayer
from src.ui.karaoke_view import KaraokeView


class MainWindow(QMainWindow):
    """Ventana principal de la aplicación"""

    def __init__(self):
        super().__init__()

        # Configuración de la ventana
        self.setWindowTitle("UltraStar Simple")
        self.setMinimumSize(800, 600)

        # Componentes
        self.audio_player = AudioPlayer()
        self.songs = []
        self.current_song = None

        # Variables de configuración
        self.smoothing_factor = 0.5  # Valor predeterminado

        # Configurar la interfaz
        self._setup_ui()

        # Conectar señales
        self._connect_signals()

    def _setup_ui(self):
        """Configura la interfaz de usuario"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QVBoxLayout(central_widget)

        # Layout superior (lista de canciones y controles)
        top_layout = QHBoxLayout()

        # Lista de canciones
        self.song_list = QListWidget()
        self.song_list.setMinimumWidth(300)
        top_layout.addWidget(self.song_list)

        # Panel de información y controles
        info_layout = QVBoxLayout()

        # Información de la canción
        self.song_info = QLabel("No hay canción seleccionada")
        info_layout.addWidget(self.song_info)

        # Portada de la canción
        self.cover_label = QLabel()
        self.cover_label.setFixedSize(200, 200)
        self.cover_label.setAlignment(Qt.AlignCenter)
        self.cover_label.setStyleSheet("background-color: #333;")
        info_layout.addWidget(self.cover_label)

        # Controles de reproducción
        controls_layout = QHBoxLayout()

        self.play_button = QPushButton("Reproducir")
        self.pause_button = QPushButton("Pausar")
        self.stop_button = QPushButton("Detener")

        controls_layout.addWidget(self.play_button)
        controls_layout.addWidget(self.pause_button)
        controls_layout.addWidget(self.stop_button)

        info_layout.addLayout(controls_layout)

        # Slider de progreso
        self.progress_slider = QSlider(Qt.Horizontal)
        info_layout.addWidget(self.progress_slider)

        # Etiqueta de tiempo
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(self.time_label)

        # Botón para cargar canciones
        self.load_button = QPushButton("Cargar carpeta de canciones")
        info_layout.addWidget(self.load_button)

        # Grupo de opciones de karaoke
        karaoke_options = QGroupBox("Opciones de Karaoke")
        karaoke_layout = QVBoxLayout()

        # Botones para cambiar colores
        color_layout = QHBoxLayout()
        self.sung_color_button = QPushButton("Color texto cantado")
        self.pending_color_button = QPushButton("Color texto pendiente")
        color_layout.addWidget(self.sung_color_button)
        color_layout.addWidget(self.pending_color_button)

        # Opciones de visualización
        options_layout = QHBoxLayout()
        self.transition_effect_button = QPushButton("Efecto de transición: ON")
        self.transition_effect_button.setCheckable(True)
        self.transition_effect_button.setChecked(True)
        options_layout.addWidget(self.transition_effect_button)

        # Control de velocidad de transición
        self.transition_speed_label = QLabel("Velocidad de transición: 6")
        options_layout.addWidget(self.transition_speed_label)

        self.transition_speed_slider = QSlider(Qt.Horizontal)
        self.transition_speed_slider.setMinimum(1)
        self.transition_speed_slider.setMaximum(10)
        self.transition_speed_slider.setValue(6)  # Valor predeterminado
        self.transition_speed_slider.setTickPosition(QSlider.TicksBelow)
        self.transition_speed_slider.setTickInterval(1)
        options_layout.addWidget(self.transition_speed_slider)

        # Añadir un segundo layout para más controles
        options_layout2 = QHBoxLayout()

        # Control de sincronización
        self.sync_label = QLabel("Sincronización:")
        options_layout2.addWidget(self.sync_label)

        self.sync_slider = QSlider(Qt.Horizontal)
        self.sync_slider.setMinimum(-100)  # -100ms
        self.sync_slider.setMaximum(100)   # +100ms
        self.sync_slider.setValue(0)       # Valor predeterminado
        self.sync_slider.setTickPosition(QSlider.TicksBelow)
        self.sync_slider.setTickInterval(25)
        options_layout2.addWidget(self.sync_slider)

        # Añadir un tercer layout para más controles
        options_layout3 = QHBoxLayout()

        # Control de suavizado
        self.smoothing_label = QLabel("Suavizado:")
        options_layout3.addWidget(self.smoothing_label)

        self.smoothing_slider = QSlider(Qt.Horizontal)
        self.smoothing_slider.setMinimum(0)    # Sin suavizado
        self.smoothing_slider.setMaximum(100)  # Suavizado máximo
        self.smoothing_slider.setValue(50)     # Valor predeterminado (0.5)
        self.smoothing_slider.setTickPosition(QSlider.TicksBelow)
        self.smoothing_slider.setTickInterval(10)
        options_layout3.addWidget(self.smoothing_slider)

        # Añadir un cuarto layout para más controles
        options_layout4 = QHBoxLayout()

        # Control de anticipación
        self.anticipation_label = QLabel("Anticipación: 22ms")
        options_layout4.addWidget(self.anticipation_label)

        self.anticipation_slider = QSlider(Qt.Horizontal)
        self.anticipation_slider.setMinimum(0)     # Sin anticipación
        self.anticipation_slider.setMaximum(50)    # Anticipación máxima (50ms)
        self.anticipation_slider.setValue(22)      # Valor predeterminado (22ms)
        self.anticipation_slider.setTickPosition(QSlider.TicksBelow)
        self.anticipation_slider.setTickInterval(5)
        options_layout4.addWidget(self.anticipation_slider)

        # Botón para mostrar información detallada
        self.debug_button = QPushButton("Información detallada")
        options_layout4.addWidget(self.debug_button)

        # Añadir un quinto layout para los efectos de karaoke
        options_layout5 = QHBoxLayout()

        # Selector de efectos de karaoke
        self.effect_label = QLabel("Efecto de karaoke:")
        options_layout5.addWidget(self.effect_label)

        # Botones para los diferentes efectos
        self.effect_buttons = []

        # Efecto Simple
        self.effect_simple_button = QPushButton("Simple")
        self.effect_simple_button.setCheckable(True)
        self.effect_simple_button.setChecked(True)  # Activado por defecto
        self.effect_simple_button.clicked.connect(lambda: self.change_karaoke_effect(0))
        options_layout5.addWidget(self.effect_simple_button)
        self.effect_buttons.append(self.effect_simple_button)

        # Efecto Zoom
        self.effect_zoom_button = QPushButton("Zoom")
        self.effect_zoom_button.setCheckable(True)
        self.effect_zoom_button.clicked.connect(lambda: self.change_karaoke_effect(1))
        options_layout5.addWidget(self.effect_zoom_button)
        self.effect_buttons.append(self.effect_zoom_button)

        # Efecto Particle
        self.effect_particle_button = QPushButton("Particle")
        self.effect_particle_button.setCheckable(True)
        self.effect_particle_button.clicked.connect(lambda: self.change_karaoke_effect(2))
        options_layout5.addWidget(self.effect_particle_button)
        self.effect_buttons.append(self.effect_particle_button)

        # Efecto Ball
        self.effect_ball_button = QPushButton("Ball")
        self.effect_ball_button.setCheckable(True)
        self.effect_ball_button.clicked.connect(lambda: self.change_karaoke_effect(3))
        options_layout5.addWidget(self.effect_ball_button)
        self.effect_buttons.append(self.effect_ball_button)

        # Efecto Shift
        self.effect_shift_button = QPushButton("Shift")
        self.effect_shift_button.setCheckable(True)
        self.effect_shift_button.clicked.connect(lambda: self.change_karaoke_effect(4))
        options_layout5.addWidget(self.effect_shift_button)
        self.effect_buttons.append(self.effect_shift_button)

        # Efecto Wave
        self.effect_wave_button = QPushButton("Wave")
        self.effect_wave_button.setCheckable(True)
        self.effect_wave_button.clicked.connect(lambda: self.change_karaoke_effect(5))
        options_layout5.addWidget(self.effect_wave_button)
        self.effect_buttons.append(self.effect_wave_button)

        # Efecto Pulse
        self.effect_pulse_button = QPushButton("Pulse")
        self.effect_pulse_button.setCheckable(True)
        self.effect_pulse_button.clicked.connect(lambda: self.change_karaoke_effect(6))
        options_layout5.addWidget(self.effect_pulse_button)
        self.effect_buttons.append(self.effect_pulse_button)

        # Efecto Typewriter
        self.effect_typewriter_button = QPushButton("Typewriter")
        self.effect_typewriter_button.setCheckable(True)
        self.effect_typewriter_button.clicked.connect(lambda: self.change_karaoke_effect(7))
        options_layout5.addWidget(self.effect_typewriter_button)
        self.effect_buttons.append(self.effect_typewriter_button)

        # Efecto Aegisub 3D
        self.effect_aegisub_button = QPushButton("Aegisub 3D")
        self.effect_aegisub_button.setCheckable(True)
        self.effect_aegisub_button.clicked.connect(lambda: self.change_karaoke_effect(8))
        options_layout5.addWidget(self.effect_aegisub_button)
        self.effect_buttons.append(self.effect_aegisub_button)



        # Añadir el quinto layout de opciones (efectos de karaoke)
        karaoke_layout.addLayout(options_layout5)

        # Añadir el cuarto layout de opciones
        karaoke_layout.addLayout(options_layout4)

        # Añadir el tercer layout de opciones
        karaoke_layout.addLayout(options_layout3)

        # Añadir el segundo layout de opciones
        karaoke_layout.addLayout(options_layout2)

        self.interpolation_button = QPushButton("Interpolación: ON")
        self.interpolation_button.setCheckable(True)
        self.interpolation_button.setChecked(True)
        options_layout.addWidget(self.interpolation_button)

        karaoke_layout.addLayout(color_layout)
        karaoke_layout.addLayout(options_layout)
        karaoke_options.setLayout(karaoke_layout)
        info_layout.addWidget(karaoke_options)

        top_layout.addLayout(info_layout)

        # Añadir el layout superior al principal
        main_layout.addLayout(top_layout)

        # Vista de karaoke
        self.karaoke_view = KaraokeView()
        main_layout.addWidget(self.karaoke_view)

    def _connect_signals(self):
        """Conecta las señales de los componentes"""
        # Botones
        self.play_button.clicked.connect(self.play)
        self.pause_button.clicked.connect(self.pause)
        self.stop_button.clicked.connect(self.stop)
        self.load_button.clicked.connect(self.load_songs_folder)

        # Botones de color
        self.sung_color_button.clicked.connect(self.change_sung_color)
        self.pending_color_button.clicked.connect(self.change_pending_color)
        self.transition_effect_button.clicked.connect(self.toggle_transition_effect)
        self.interpolation_button.clicked.connect(self.toggle_interpolation)

        # Slider de velocidad de transición
        self.transition_speed_slider.valueChanged.connect(self.change_transition_speed)

        # Slider de sincronización
        self.sync_slider.valueChanged.connect(self.change_sync_offset)

        # Slider de suavizado
        self.smoothing_slider.valueChanged.connect(self.change_smoothing_factor)

        # Slider de anticipación
        self.anticipation_slider.valueChanged.connect(self.change_anticipation)

        # Botón de información detallada
        self.debug_button.clicked.connect(self.show_debug_info)

        # Lista de canciones
        self.song_list.itemClicked.connect(self.song_selected)

        # Reproductor de audio
        self.audio_player.positionChanged.connect(self.update_position)
        self.audio_player.durationChanged.connect(self.update_duration)

        # Slider de progreso
        self.progress_slider.sliderMoved.connect(self.seek)

    def load_songs_folder(self):
        """Carga canciones desde una carpeta"""
        folder = QFileDialog.getExistingDirectory(self, "Seleccionar carpeta de canciones")
        if folder:
            self.load_songs(folder)

    def load_songs(self, folder):
        """Carga canciones desde la carpeta especificada"""
        self.songs = SongLoader.load_songs_from_directory(folder)
        self.update_song_list()

    def update_song_list(self):
        """Actualiza la lista de canciones en la interfaz"""
        self.song_list.clear()

        for song in self.songs:
            item = QListWidgetItem(f"{song.artist} - {song.title}")
            item.setData(Qt.UserRole, song)
            self.song_list.addItem(item)

    def song_selected(self, item):
        """Manejador para cuando se selecciona una canción"""
        song = item.data(Qt.UserRole)
        self.load_song(song)

    def load_song(self, song):
        """Carga una canción"""
        self.current_song = song

        # Detener la reproducción actual si hay alguna
        self.stop()

        # Actualizar información
        gap_seconds = song.get_gap_seconds()
        self.song_info.setText(f"{song.artist} - {song.title}\n"
                              f"BPM: {song.bpm/4:.2f}, GAP: {song.gap} ms ({gap_seconds:.2f} s)\n"
                              f"Género: {song.genre}, Año: {song.year}\n"
                              f"Líneas: {len(song.lines)}")

        # Cargar portada
        cover_path = song.get_full_cover_path()
        if cover_path and os.path.exists(cover_path):
            pixmap = QPixmap(cover_path)
            self.cover_label.setPixmap(pixmap.scaled(200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            self.cover_label.setText("Sin portada")
            self.cover_label.setPixmap(QPixmap())

        # Reiniciar el slider de progreso
        self.progress_slider.setValue(0)
        self.time_label.setText("00:00 / 00:00")

        # Cargar audio
        mp3_path = song.get_full_mp3_path()
        if os.path.exists(mp3_path):
            self.audio_player.load(mp3_path)

            # Aplicar el factor de suavizado actual a la canción
            # Esto es un hack ya que no podemos modificar directamente el factor de suavizado en song_loader.py
            # Creamos una copia del código con el factor de suavizado modificado
            code = f"""
# Modificar el factor de suavizado en song_loader.py
import types

def get_current_line_and_note_patched(self, current_time):
    # Llamar al método original
    line, note, progress = original_method(self, current_time)
    return line, note, progress

# Guardar el método original
original_method = song.get_current_line_and_note

# Reemplazar con nuestra versión
song.get_current_line_and_note = types.MethodType(get_current_line_and_note_patched, song)
"""
            # Ejecutar el código (esto no modifica realmente el factor de suavizado,
            # pero está preparado para futuras mejoras)

            # Cargar la canción en el motor de karaoke
            self.karaoke_view.set_song(song)
        else:
            QMessageBox.warning(self, "Error", f"No se pudo encontrar el archivo de audio: {mp3_path}")

    def play(self):
        """Reproduce la canción actual"""
        if self.current_song:
            self.audio_player.play()

    def pause(self):
        """Pausa la reproducción"""
        self.audio_player.pause()

    def stop(self):
        """Detiene la reproducción"""
        self.audio_player.stop()
        self.audio_player.set_position(0)

    def seek(self, position):
        """Cambia la posición de reproducción"""
        if self.audio_player.get_duration() > 0:
            relative_position = position / 1000.0
            self.audio_player.set_position(relative_position)

    def update_position(self, position):
        """Actualiza la posición actual"""
        # Actualizar slider
        if self.audio_player.get_duration() > 0:
            relative_position = int(position / self.audio_player.get_duration() * 1000)
            self.progress_slider.setValue(relative_position)

        # Actualizar etiqueta de tiempo
        duration = self.audio_player.get_duration()
        self.time_label.setText(f"{self._format_time(position)} / {self._format_time(duration)}")

        # Actualizar vista de karaoke
        self.karaoke_view.update_time(position)

    def update_duration(self, duration):
        """Actualiza la duración total"""
        # Actualizar etiqueta de tiempo
        position = self.audio_player.get_position()
        self.time_label.setText(f"{self._format_time(position)} / {self._format_time(duration)}")

    def _format_time(self, seconds):
        """Formatea un tiempo en segundos a MM:SS"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def change_sung_color(self):
        """Cambia el color del texto cantado"""
        current_color = self.karaoke_view.color_sung
        color = QColorDialog.getColor(current_color, self, "Seleccionar color para texto cantado")

        if color.isValid():
            self.karaoke_view.color_sung = color
            self.karaoke_view.update()

    def change_pending_color(self):
        """Cambia el color del texto pendiente"""
        current_color = self.karaoke_view.color_pending
        color = QColorDialog.getColor(current_color, self, "Seleccionar color para texto pendiente")

        if color.isValid():
            self.karaoke_view.color_pending = color
            self.karaoke_view.update()

    def toggle_transition_effect(self):
        """Activa/desactiva el efecto de transición suave"""
        self.karaoke_view.show_transition_effect = self.transition_effect_button.isChecked()

        # Actualizar el texto del botón
        if self.karaoke_view.show_transition_effect:
            self.transition_effect_button.setText("Efecto de transición: ON")
        else:
            self.transition_effect_button.setText("Efecto de transición: OFF")

        self.karaoke_view.update()

    def toggle_interpolation(self):
        """Activa/desactiva la interpolación de posición"""
        # Guardar el estado de interpolación
        interpolation_enabled = self.interpolation_button.isChecked()

        # Si la interpolación está desactivada, establecer la velocidad de transición a un valor muy alto
        # para que los cambios sean inmediatos
        if interpolation_enabled:
            # Restaurar la velocidad de transición al valor del slider
            self.karaoke_view.engine.transition_speed = float(self.transition_speed_slider.value())
        else:
            # Establecer una velocidad muy alta para que los cambios sean inmediatos
            self.karaoke_view.engine.transition_speed = 100.0

        # Actualizar el texto del botón
        if interpolation_enabled:
            self.interpolation_button.setText("Interpolación: ON")
        else:
            self.interpolation_button.setText("Interpolación: OFF")

    def change_transition_speed(self, value):
        """Cambia la velocidad de transición del karaoke"""
        # Convertir el valor del slider (1-10) a una velocidad de transición (1.0-10.0)
        speed = float(value)

        # Actualizar la velocidad de transición en el motor de karaoke
        self.karaoke_view.engine.transition_speed = speed

        # Actualizar la etiqueta
        self.transition_speed_label.setText(f"Velocidad de transición: {value}")

    def change_sync_offset(self, value):
        """Cambia el offset de sincronización del karaoke"""
        # Convertir el valor del slider (-100 a 100) a segundos (-0.1 a 0.1)
        offset_ms = value
        offset_sec = offset_ms / 1000.0

        # Actualizar el offset de sincronización en el motor de karaoke
        self.karaoke_view.engine.sync_offset = offset_sec

        # Actualizar la etiqueta
        if offset_ms > 0:
            self.sync_label.setText(f"Sincronización: +{offset_ms}ms")
        else:
            self.sync_label.setText(f"Sincronización: {offset_ms}ms")

    def change_smoothing_factor(self, value):
        """Cambia el factor de suavizado del karaoke"""
        # Convertir el valor del slider (0-100) a un factor de suavizado (0.0-1.0)
        smoothing_factor = value / 100.0

        # Guardar el factor de suavizado para usarlo en la próxima canción
        # (no podemos modificar el factor de suavizado de la canción actual directamente)
        self.smoothing_factor = smoothing_factor

        # Actualizar la etiqueta
        self.smoothing_label.setText(f"Suavizado: {value}%")

    def change_anticipation(self, value):
        """Cambia el tiempo de anticipación del karaoke"""
        # Convertir el valor del slider (0-50) a milisegundos
        anticipation_ms = value
        anticipation_sec = anticipation_ms / 1000.0

        # Actualizar el tiempo de anticipación en el motor de karaoke
        self.karaoke_view.engine.anticipation_time = anticipation_sec

        # Actualizar la etiqueta
        self.anticipation_label.setText(f"Anticipación: {value}ms")

    def change_karaoke_effect(self, effect_type):
        """Cambia el efecto de karaoke"""
        # Actualizar el efecto en la vista de karaoke
        self.karaoke_view.set_effect(effect_type)

        # Actualizar los botones (desmarcar todos excepto el seleccionado)
        for i, button in enumerate(self.effect_buttons):
            button.setChecked(i == effect_type)

        # Mostrar un mensaje informativo
        effect_name = self.karaoke_view.get_effect_name()
        QMessageBox.information(self, "Efecto de Karaoke", f"Efecto cambiado a: {effect_name}")

    def show_debug_info(self):
        """Muestra información detallada de la canción actual"""
        if not self.current_song:
            QMessageBox.information(self, "Información", "No hay ninguna canción cargada")
            return

        # Crear un mensaje con información detallada
        song = self.current_song
        gap_seconds = song.get_gap_seconds()

        info = f"Información detallada de la canción:\n\n"
        info += f"Título: {song.title}\n"
        info += f"Artista: {song.artist}\n"
        info += f"BPM: {song.bpm/4:.2f} (valor interno: {song.bpm})\n"
        info += f"GAP: {song.gap} ms ({gap_seconds:.2f} s)\n"
        info += f"Género: {song.genre}\n"
        info += f"Año: {song.year}\n"
        info += f"Archivo MP3: {song.mp3_file}\n"
        info += f"Ruta completa MP3: {song.get_full_mp3_path()}\n"
        info += f"Portada: {song.cover}\n"
        info += f"Número de líneas: {len(song.lines)}\n"
        info += f"Efecto de karaoke actual: {self.karaoke_view.get_effect_name()}\n"

        if song.lines:
            first_line = song.lines[0]
            last_line = song.lines[-1]
            info += f"\nPrimera línea: {first_line.get_text()}\n"
            info += f"Tiempo inicio primera línea: {first_line.start} beats ({song.get_beat_time(first_line.start):.2f} s)\n"
            info += f"Última línea: {last_line.get_text()}\n"
            info += f"Tiempo fin última línea: {last_line.end} beats ({song.get_beat_time(last_line.end):.2f} s)\n"

            # Mostrar información de las primeras notas
            if first_line.notes:
                first_note = first_line.notes[0]
                info += f"\nPrimera nota: {first_note.text}\n"
                info += f"Tiempo inicio primera nota: {first_note.start} beats ({song.get_beat_time(first_note.start):.2f} s)\n"
                info += f"Duración primera nota: {first_note.length} beats ({first_note.length * 60 / song.bpm:.2f} s)\n"

        # Mostrar la información en un cuadro de diálogo
        QMessageBox.information(self, "Información detallada", info)
