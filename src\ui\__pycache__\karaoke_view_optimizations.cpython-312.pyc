�

    ��h�5  �                   �   � d dl mZmZmZmZmZmZmZmZ d dl	m
Z
mZmZm
Z
mZmZ d dlmZ d dlZd dlZd dlZd dlZ G d� de�      Z G d� d�      Z G d	� d
e�      Zy)�    )�QObject�QThread�
pyqtSignal�QMutex�QSize�QTimer�QRect�Qt)�QImage�QPixmap�QPainter�QColor�QFont�QPainterPath)�QWidgetNc                   �f   � � e Zd ZdZ eee�      Zd� fd�	Zd� Z	d� Z
d� Zd� Zd� Z
d� Zd	� Zd
� Z� xZS )�
AsyncRendereru>   Clase para renderizar efectos de karaoke de forma asincrónicac                 �  �� t         �| �  |�       t        j                  �       | _        i | _        t
        �       | _        t        j                  j                  d��      | _        d | _        d| _
        | j                  �        y )N�   )�max_workersF)�super�__init__�queue�Queue�render_queue�render_cacher   �cache_mutex�
concurrent�futures�ThreadPoolExecutor�executor�processing_thread�stop_processing�start_processing��self�parent�	__class__s     ��NC:\Users\<USER>\Desktop\ultrastar_basics\src\ui\karaoke_view_optimizations.pyr   zAsyncRenderer.__init__   sg   �� �
���� �!�K�K�M������!�8���"�*�*�=�=�!�=�L��
�!%���$�������    c                 �   � | j                   �| j                   j                  �       sXd| _        t        j                  | j
                  ��      | _         d| j                   _        | j                   j                  �        yy)zInicia el hilo de procesamientoNF��targetT)r"   �is_aliver#   �	threading�Thread�_process_queue�daemon�start�r&   s    r)   r$   zAsyncRenderer.start_processing#   sf   � ��!�!�)��1G�1G�1P�1P�1R�#(�D� �%.�%5�%5�T�=P�=P�%Q�D�"�,0�D�"�"�)��"�"�(�(�*�	 2Sr*   c                 �  � d| _         | j                  rQ| j                  j                  �       r7| j                  j	                  d�       | j                  j                  d��       | j                  j                  d��       y)z Detiene el hilo de procesamientoTN�      �?��timeoutF)�wait)r#   r"   r.   r   �put�joinr!   �shutdownr4   s    r)   �stopzAsyncRenderer.stop+   sd   � �#����!�!�d�&<�&<�&E�&E�&G����!�!�$�'��"�"�'�'��'�4��
�
���E��*r*   c                 �r  � | j                   �sX	 | j                  j                  d��      }|�y|\  }}}}| j                  j	                  �        || j
                  v r5| j                  j
                  �        | j                  j                  �        ��| j                  j
                  �         | j                  j                  |g|��i |��}|j                  �       }|�_| j                  j	                  �        || j
                  |<   | j                  j
                  �        | j                  j                  ||�       | j                  j                  �        | j                   s��Wyy# t        j                  $ r Y �%t        $ r2}t!        d|� ��       | j                  j                  �        Y d}~�Zd}~ww xY w)zProcesa la cola de renderizado皙�����?r7   N�!Error en el hilo de renderizado: )r#   r   �getr   �lockr   �unlock�	task_doner!   �submit�result�render_complete�emitr   �Empty�	Exception�print)	r&   �task�key�render_func�args�kwargs�futurerF   �es	            r)   r1   zAsyncRenderer._process_queue3   s  � ��&�&�$
.��(�(�,�,�S�,�9���<�� 26�.��[�$�� � � �%�%�'��$�+�+�+��$�$�+�+�-��%�%�/�/�1��� � �'�'�)� .����-�-�k�K�D�K�F�K��������%��$�$�)�)�+�-3�D�%�%�c�*��$�$�+�+�-� �(�(�-�-�c�6�:��!�!�+�+�-�? �&�&��@ �;�;� 
��� 
.��9�!��=�>��!�!�+�+�-�-��
.�s*   �E' �A#E' �CE' �'F6�<F6�(F1�1F6c                 �R  � | j                   j                  �        || j                  v rF| j                  |   }| j                   j                  �        | j                  j                  ||�       y| j                   j                  �        | j                  j                  ||||f�       y)zEncola una tarea de renderizadoTF)r   rB   r   rC   rG   rH   r   r:   )r&   rM   rN   rO   rP   rF   s         r)   �queue_renderzAsyncRenderer.queue_render\   s�   � � 	
�������$�#�#�#��&�&�s�+�F����#�#�%�� � �%�%�c�6�2�������!� 	
�����s�K��v�>�?�r*   c                 �   � | j                   j                  �        | j                  j                  |d�      }| j                   j	                  �        |S )u   Obtiene una imagen de la cachéN)r   rB   r   rA   rC   �r&   rM   rF   s      r)   �get_from_cachezAsyncRenderer.get_from_cachel   sC   � ��������"�"�&�&�s�D�1�������!��
r*   c                 �   � | j                   j                  �        | j                  j                  �        | j                   j	                  �        y)u   Limpia la caché de renderizadoN)r   rB   r   �clearrC   r4   s    r)   �clear_cachezAsyncRenderer.clear_caches   s8   � ������������!������!r*   c                 ��   � t        ||t         j                  �      }|j                  t        dddd�      �       t	        d�      D ]2  }dD ]+  }d|� d|� �}| j                  || j                  |||||�       �- �4 y)z:Precarga efectos comunes para mejorar la respuesta inicialr   �	   �g        g      �?g      �?g      �?r6   �effect_�
_progress_N)r   �
Format_ARGB32�fillr   �rangerT   �_render_effect_preview)r&   �view�width�height�blank_image�effect�progressrM   s           r)   �preload_effectszAsyncRenderer.preload_effectsy   s�   � � �U�F�F�,@�,@�A�������1�a��+�,� �A�h�F�7����x�z�(��<���!�!�#�t�'B�'B�D�&�RZ�\a�ci�j� 8� r*   c                 ��  � t        ||t         j                  �      }|j                  t        ddd�      �       t	        |�      }|j                  t        j                  d�       d}t        ddt        j                  �      }	|j                  |	�       ||j                  �       j                  |�      z
  dz  }
|dz  }t        �       }|j                  t        |
�      t        |�      |	|�       |j                  t        dddd�      �       |j!                  t        ddd�      �       |j#                  |�       t        |j                  �       j                  |�      |z  �      }
|
dkD  ru|j%                  �        t'        t        |
�      d|
|�      }|j)                  |�       |j!                  t        ddd�      �       |j#                  |�       |j+                  �        |j-                  �        |S )z'Renderiza una vista previa de un efector   T�Ejemplo�Arial�   r   ��   �r   r`   ra   r   r
   �
setRenderHint�TextAntialiasingr   �Bold�setFont�fontMetrics�horizontalAdvancer   �addText�int�setPen�setBrush�drawPath�saver	   �setClipRect�restore�end)r&   rd   rh   ri   re   rf   �image�painter�text�font�x�y�path�
clip_width�	clip_rects                  r)   rc   z$AsyncRenderer._render_effect_preview�   s�  � � �u�f�f�&:�&:�;��
�
�
�6�!�Q��?�#� �5�/�� 	���h�7�7��>� �� �W�b�%�*�*�-������� �W�(�(�*�<�<�T�B�
B�a�G���Q�J�� �~�����S��V�S��V�T�4�0� 	���v�a��A�q�)�*������S�#�.�/������ ��,�,�.�@�@��F��Q�R�
���>��L�L�N��c�!�f�a��V�<�I����	�*����V�A�s�A�.�/����T�"��O�O�� 	���
��r*   �N)�__name__�
__module__�__qualname__�__doc__r   �strr   rG   r   r$   r=   r1   rT   rW   rZ   rj   rc   �
__classcell__�r(   s   @r)   r   r   
   sA   �� �H� ��f�-�O� �"+�+�'.�R� �"�
k�0r*   r   c                   �0   � e Zd ZdZdd�Zd� Zd� Zd� Zd� Zy)	�RenderCacheuC   Clase para gestionar la caché de renderizado de efectos de karaokec                 �L   � i | _         || _        t        �       | _        i | _        y r�   )�cache�max_sizer   �mutex�last_access)r&   r�   s     r)   r   zRenderCache.__init__�   s!   � ���
� ��
��X��
���r*   c                 ��   � | j                   j                  �        | j                  j                  |d�      }|�!t	        j                  �       | j
                  |<   | j                   j
                  �        |S )u    Obtiene un elemento de la cachéN)r�   rB   r�   rA   �timer�   rC   rV   s      r)   rA   zRenderCache.get�   sU   � ��
�
����������T�*����$(�I�I�K�D���S�!��
�
�����
r*   c                 �  � | j                   j                  �        t        | j                  �      | j                  k\  rCt        | j                  j                  �       d� ��      d   }| j                  |= | j                  |= || j                  |<   t        j                  �       | j                  |<   | j                   j                  �        y)u   Añade un elemento a la cachéc                 �   � | d   S )N�   � )r�   s    r)   �<lambda>z!RenderCache.put.<locals>.<lambda>�   s   � �Q�q�Tr*   )rM   r   N)
r�   rB   �lenr�   r�   �minr�   �itemsr�   rC   )r&   rM   �value�
oldest_keys       r)   r:   zRenderCache.put�   s�   � ��
�
���� �t�z�z�?�d�m�m�+��T�-�-�3�3�5�>�J�1�M�J��
�
�:�&�� � ��,�  ��
�
�3�� $�	�	��������
�
���r*   c                 ��   � | j                   j                  �        | j                  j                  �        | j                  j                  �        | j                   j                  �        y)u   Limpia la cachéN)r�   rB   r�   rY   r�   rC   r4   s    r)   rY   zRenderCache.clear�   sB   � ��
�
�����
�
��������� ��
�
���r*   c                 �   � | j                   j                  �        t        | j                  �      }| j                   j	                  �        |S )u'   Devuelve el tamaño actual de la caché)r�   rB   r�   r�   rC   )r&   �sizes     r)   r�   zRenderCache.size�   s2   � ��
�
�����4�:�:����
�
�����r*   N)�d   )	r�   r�   r�   r�   r   rA   r:   rY   r�   r�   r*   r)   r�   r�   �   s   � �M����$�r*   r�   c                   �Z   � � e Zd ZdZ eee�      Zd	� fd�	Zd� Z	d� Z
d� Zd� Zd� Z
d� Z� xZS )
�BackgroundRendererz9Clase para renderizar efectos de karaoke en segundo planoc                 �   �� t         �| �  |�       t        d��      | _        t	        j
                  �       | _        d | _        d| _        | j                  �        y )N��   )r�   F)
r   r   r�   r�   r   �
PriorityQueue�
pending_tasks�
render_thread�stop_rendering�start_renderingr%   s     �r)   r   zBackgroundRenderer.__init__�   sI   �� �
���� � �#�.��
�"�0�0�2���!���#������r*   c                 �   � | j                   �| j                   j                  �       sXd| _        t        j                  | j
                  ��      | _         d| j                   _        | j                   j                  �        yy)zInicia el hilo de renderizadoNFr,   T)r�   r.   r�   r/   r0   �_render_loopr2   r3   r4   s    r)   r�   z"BackgroundRenderer.start_rendering�   sf   � ����%�T�-?�-?�-H�-H�-J�"'�D��!*�!1�!1��9J�9J�!K�D��(,�D���%����$�$�&�	 .Kr*   c                 ��   � d| _         | j                  rS| j                  j                  �       r8| j                  j	                  d�       | j                  j                  d��       yyy)zDetiene el hilo de renderizadoT)r   Nr6   r7   N)r�   r�   r.   r�   r:   r;   r4   s    r)   r=   zBackgroundRenderer.stop  sX   � �"������$�"4�"4�"=�"=�"?����"�"�9�-����#�#�C�#�0� #@�r*   c                 �  � | j                   s�	 | j                  j                  d��      \  }}|�y|\  }}}}| j                  j                  |�      �| j                  j	                  �        �l	  ||i |��}|�8| j                  j                  ||�       | j                  j                  ||�       | j                  j	                  �        | j                   s��yy# t        $ r}t        d|� ��       Y d}~�Ed}~ww xY w# t        j                  $ r Y �Ht        $ r>}t        d|� ��       dt        �       v r| j                  j	                  �        Y d}~��d}~ww xY w)zBucle principal de renderizador?   r7   NzError en el renderizado: r@   rL   )
r�   r�   rA   r�   rD   r:   rG   rH   rJ   rK   r   rI   �locals)	r&   �priorityrL   rM   rN   rO   rP   rF   rR   s	            r)   r�   zBackgroundRenderer._render_loop  sB  � ��%�%� 
3�!%�!3�!3�!7�!7��!7�!D���$��<�� 26�.��[�$�� �:�:�>�>�#�&�2��&�&�0�0�2��;�(�$�9�&�9�F��)��
�
���s�F�3��,�,�1�1�#�v�>� �"�"�,�,�.�5 �%�%��. !� ;��5�a�S�9�:�:��;�� �;�;� 
��� 
3��9�!��=�>��V�X�%��&�&�0�0�2���
3�sM   �!C= �<C= �.AC �0C= �	C:�"C5�0C= �5C:�:C= �=E�E�4E�Ec                 �   � | j                   j                  |�      }|�| j                  j                  ||�       y| j                  j                  |||||ff�       y)z-Encola una tarea de renderizado con prioridadTF)r�   rA   rG   rH   r�   r:   )r&   rM   r�   rN   rO   rP   �cacheds          r)   rT   zBackgroundRenderer.queue_render1  s\   � � ������$����� � �%�%�c�6�2�� 	
������3��T�6�*J�K�L�r*   c                 �   � g d�}t        d�      D ]>  }|D ]7  }dD ]0  }d|� d|� d|� �}| j                  |d| j                  ||||||�	       �2 �9 �@ y)	z7Pre-renderiza efectos comunes para mejorar la respuesta)rl   �Karaokeu   Músicau   Canciónr\   r]   r^   �_text_r_   r�   N)rb   rT   �_render_effect_sample)	r&   rd   re   rf   �sample_textsrh   r�   ri   rM   s	            r)   �prerender_effectsz$BackgroundRenderer.prerender_effects>  sn   � � E�� �A�h�F�$�� ;�H�#�F�8�6�$��z�(��L�C��%�%�c�3��0J�0J�%)�6�4��5�&�R� !<� %� r*   c                 ��  � t        ||t         j                  �      }|j                  t        ddd�      �       t	        |�      }|j                  t        j                  d�       t        ddt        j                  �      }	|j                  |	�       ||j                  �       j                  |�      z
  dz  }
|dz  }t        �       }|j                  t        |
�      t        |�      |	|�       |j                  t        dddd�      �       |j!                  t        ddd�      �       |j#                  |�       t        |j                  �       j                  |�      |z  �      }
|
dkD  ru|j%                  �        t'        t        |
�      d|
|�      }|j)                  |�       |j!                  t        ddd�      �       |j#                  |�       |j+                  �        |j-                  �        |S )z0Renderiza una muestra de un efecto para precargar   Trm   rn   r   ro   rp   )r&   rd   rh   r�   ri   re   rf   r�   r�   r�   r�   r�   r�   r�   r�   s                  r)   r�   z(BackgroundRenderer._render_effect_sampleL  s�  � � �u�f�f�&:�&:�;��
�
�
�6�!�Q��?�#� �5�/�� 	���h�7�7��>� �W�b�%�*�*�-������� �W�(�(�*�<�<�T�B�
B�a�G���Q�J�� �~�����S��V�S��V�T�4�0� 	���v�a��A�q�)�*������S�#�.�/������ ��,�,�.�@�@��F��Q�R�
���>��L�L�N��c�!�f�a��V�<�I����	�*����V�A�s�A�.�/����T�"��O�O�� 	���
��r*   r�   )r�   r�   r�   r�   r   r�   r   rG   r   r�   r=   r�   rT   r�   r�   r�   r�   s   @r)   r�   r�   �   s7   �� �C� ��f�-�O��'�1�#3�J�R�*r*   r�   )�PyQt5.QtCorer   r   r   r   r   r   r	   r
   �PyQt5.QtGuir   r   r
   r   r   r   �PyQt5.QtWidgetsr   �concurrent.futuresr   r/   r   r�   r   r�   r�   r�   r*   r)   �<module>r�      sN   �� X� W� W� N� N� #� � � � �h�G� h�V0� 0�fK�� Kr*   
