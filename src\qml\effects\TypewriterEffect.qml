import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "TypewriterEffect"
    
    // Parámetros exactos del QPainter original
    property real typewriterCharDelay: 0.15
    property real typewriterFadeDuration: 0.2
    
    // Timer para cursor parpadeante (independiente)
    property real cursorBlinkTime: 0.0
    
    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS
        onTriggered: {
            root.cursorBlinkTime += 0.016
        }
    }
    
    // Propiedades seguras
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0
    
    // TextMetrics para cálculos precisos de posición
    TextMetrics {
        id: sungTextMetrics
        font: root.textFont
        text: root.safeSungText
    }
    
    TextMetrics {
        id: currentSyllableMetrics
        font: root.textFont
        text: root.safeCurrentSyllable
    }
    
    // TextMetrics para cada carácter individual (para posicionamiento preciso)
    Repeater {
        id: charMetricsRepeater
        model: root.safeCurrentSyllable.length
        
        TextMetrics {
            font: root.textFont
            text: root.safeCurrentSyllable.substring(0, index + 1) // Texto acumulativo hasta este carácter
        }
    }
    
    // Función para obtener la posición de un carácter específico
    function getCharPosition(charIndex) {
        if (charIndex <= 0) return 0
        if (charIndex >= charMetricsRepeater.count) return currentSyllableMetrics.width
        
        var prevMetrics = charMetricsRepeater.itemAt(charIndex - 1)
        return prevMetrics ? prevMetrics.width : (charIndex * currentSyllableMetrics.width / root.safeCurrentSyllable.length)
    }
    
    // Tamaño adaptativo del cursor
    readonly property real adaptiveCursorWidth: Math.max(2, root.textFont.pixelSize * 0.08)
    readonly property real adaptiveCursorHeight: root.textFont.pixelSize * 1.2
    
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0
        
        // Texto cantado (sin efectos)
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }
        
        // Sílaba actual con efecto typewriter (EXACTO al original QPainter pero adaptativo)
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height
            
            // Texto de referencia invisible (para obtener dimensiones)
            Text {
                id: currentSyllableText
                text: root.safeCurrentSyllable
                font: root.textFont
                color: "transparent"
                antialiasing: true
            }
            
            // Fondo negro para borrar cualquier texto base
            Rectangle {
                anchors.fill: parent
                color: "black"
            }
            
            // Caracteres que aparecen gradualmente con posicionamiento preciso
            Repeater {
                model: root.safeCurrentSyllable.length
                
                Item {
                    property int charIndex: index
                    property int charCount: root.safeCurrentSyllable.length
                    property real charProgress: (charIndex + 1) / charCount
                    property int visibleChars: Math.min(charCount, Math.floor(charCount * root.safeSyllableProgress) + 1)
                    property bool isVisible: charIndex < visibleChars
                    property bool isActive: charIndex === (visibleChars - 1) // Último carácter visible
                    
                    // Cálculo EXACTO de opacidad (igual que en QPainter)
                    property real charOpacity: {
                        if (!isVisible) return 0.0
                        
                        var opacity = Math.min(1.0, (root.safeSyllableProgress - (charProgress - 0.1)) / root.typewriterFadeDuration)
                        return Math.max(0.0, opacity)
                    }
                    
                    // Posición precisa basada en TextMetrics
                    x: root.getCharPosition(charIndex)
                    width: {
                        var nextPos = root.getCharPosition(charIndex + 1)
                        var currentPos = root.getCharPosition(charIndex)
                        return nextPos - currentPos
                    }
                    height: currentSyllableText.height
                    
                    Text {
                        id: charText
                        text: (parent.charIndex < root.safeCurrentSyllable.length) ? root.safeCurrentSyllable.charAt(parent.charIndex) : ""
                        font: root.textFont
                        antialiasing: true
                        
                        // Color EXACTO del QPainter original
                        color: {
                            if (parent.isActive) {
                                // Color más brillante para el carácter activo (como en QPainter)
                                return Qt.lighter(root.sungColor, 1.4)
                            } else {
                                // Color normal para caracteres ya cantados
                                return root.sungColor
                            }
                        }
                        
                        // Opacidad calculada exactamente como en QPainter
                        opacity: parent.charOpacity
                        
                        // Escala sutil para el efecto de aparición
                        scale: parent.isVisible ? (0.3 + 0.7 * Math.min(1.0, parent.charOpacity * 2)) : 0.3
                        
                        Behavior on opacity {
                            NumberAnimation {
                                duration: 100
                                easing.type: Easing.OutQuad
                            }
                        }
                        
                        Behavior on scale {
                            NumberAnimation {
                                duration: 150
                                easing.type: Easing.OutBack
                            }
                        }
                        
                        Behavior on color {
                            ColorAnimation {
                                duration: 200
                                easing.type: Easing.OutQuad
                            }
                        }
                    }
                }
            }
            
            // Caracteres pendientes en la sílaba actual con posicionamiento preciso
            Repeater {
                model: {
                    var visibleChars = Math.min(root.safeCurrentSyllable.length, Math.floor(root.safeCurrentSyllable.length * root.safeSyllableProgress) + 1)
                    return Math.max(0, root.safeCurrentSyllable.length - visibleChars)
                }
                
                Item {
                    property int charIndex: {
                        var visibleChars = Math.min(root.safeCurrentSyllable.length, Math.floor(root.safeCurrentSyllable.length * root.safeSyllableProgress) + 1)
                        return visibleChars + index
                    }
                    
                    // Posición precisa basada en TextMetrics
                    x: root.getCharPosition(charIndex)
                    width: {
                        var nextPos = root.getCharPosition(charIndex + 1)
                        var currentPos = root.getCharPosition(charIndex)
                        return nextPos - currentPos
                    }
                    height: currentSyllableText.height
                    
                    Text {
                        text: (parent.charIndex < root.safeCurrentSyllable.length) ? root.safeCurrentSyllable.charAt(parent.charIndex) : ""
                        font: root.textFont
                        color: root.pendingColor
                        opacity: 0.5 // Opacidad fija como en QPainter
                        antialiasing: true
                    }
                }
            }
            
            // Cursor parpadeante con posición y tamaño adaptativos
            Rectangle {
                x: {
                    var visibleChars = Math.min(root.safeCurrentSyllable.length, Math.floor(root.safeCurrentSyllable.length * root.safeSyllableProgress) + 1)
                    return root.getCharPosition(visibleChars)
                }
                y: -root.adaptiveCursorHeight * 0.1 // Ligeramente arriba del texto
                width: root.adaptiveCursorWidth
                height: root.adaptiveCursorHeight
                color: root.sungColor
                
                // Parpadeo EXACTO como en QPainter
                visible: {
                    if (root.safeSyllableProgress >= 1.0) return false
                    return Math.sin(root.safeSyllableProgress * 10) > 0
                }
                
                // Animación suave del parpadeo
                opacity: visible ? 1.0 : 0.0
                
                Behavior on opacity {
                    NumberAnimation { duration: 100 }
                }
            }
        }
        
        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            opacity: 0.7
            antialiasing: true
        }
    }
    
    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }
    
    // Debug info adaptativo
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 250
        height: 120
        color: "black"
        border.color: "cyan"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode
        
        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2
            
            Text {
                text: "Font Size: " + root.textFont.pixelSize + "px"
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Sung Width: " + sungTextMetrics.width.toFixed(1) + "px"
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Current Width: " + currentSyllableMetrics.width.toFixed(1) + "px"
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Cursor Size: " + root.adaptiveCursorWidth.toFixed(1) + "x" + root.adaptiveCursorHeight.toFixed(1)
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Visible Chars: " + Math.min(root.safeCurrentSyllable.length, Math.floor(root.safeCurrentSyllable.length * root.safeSyllableProgress) + 1) + "/" + root.safeCurrentSyllable.length
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Cursor Pos: " + root.getCharPosition(Math.min(root.safeCurrentSyllable.length, Math.floor(root.safeCurrentSyllable.length * root.safeSyllableProgress) + 1)).toFixed(1) + "px"
                color: "cyan"
                font.pixelSize: 9
            }
        }
    }
}
