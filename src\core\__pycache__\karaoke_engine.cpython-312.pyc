�

    ��hx$  �                   �@   � d dl mZmZmZ d dlmZmZmZ  G d� d�      Zy)�    )�List�Tuple�Optional)�Song�Line�Notec                   �   � e Zd ZdZddefd�Zdefd�Zdefd�Zde	fd	�Z
dee	e	e	f   fd
�Zde	fd�Z
defd�Zdefd
�Zdefd�Zy)�
KaraokeEnginez,Motor para el resaltado de letras de karaokeN�songc                 �   � || _         d| _        d | _        d | _        d| _        d | _        d| _        d| _        d| _        d| _	        d| _
        y )N�        g      @g�I+��?)r   �current_time�current_line�current_note�
note_progress�	last_note�target_progress�last_update_time�transition_speed�sync_offset�anticipation_time��selfr   s     �DC:\Users\<USER>\Desktop\ultrastar_basics\src\core\karaoke_engine.py�__init__zKaraokeEngine.__init__   s\   � ���	���� ��� ��� ������  #��� #��� #��� ���!&���    c                 �t   � || _         d| _        d| _        d| _        d| _        d| _        d| _        d| _        y)u   Establece la canción actualr
   N)r   r   r   r   r   r   r   r   r   s     r   �set_songzKaraokeEngine.set_song   sB   � ���	���� ��� ��� ������  #��� #��r   r   c                 �x  � | j                   sy| j                   j                  �       }||k  r|| _        d| _        d| _        d| _        y|| _        || j                  z   }| j                   j                  |�      \  | _        | _        }|| j                  z
  }|| _        | j                  dk(  s| j                  | j                  k7  r || _	        || _        | j                  | _
        y|dkD  r|| j                  z
  |z  nd}|| _	        |}| j                  r;|dkD  r6| j                  }|dkD  r|dz  }	n
|dk  r|dz  }	n|}	t        d|||	z  z   �      }t        | j
                  |z
  �      dkD  r�t        | j
                  |z
  �      }
| j                  |z  d|
d	z  z   z  }| j                  |z  d
z  }t        ||�      }
t        |
d�      }|| j
                  kD  rt        | j
                  |z   |�      | _        n7| j                  | j                  k7  r|| _        n|| j
                  k\  r|| _        | j                  | _
        y)z�
        Actualiza el estado del motor de karaoke basado en el tiempo actual

        Args:
            current_time: Tiempo actual en segundos
        Nr
   r   �      �?g�������?g      �?g�������?g����MbP?g      @g      �?g�������?)r   �get_gap_secondsr   r   r   r   r   �get_current_line_and_noter   r   r   r   �min�absr   �max)r   r   �gap_seconds�
adjusted_timer   �dt�
progress_rate�predictive_target�base_prediction_time�prediction_time�diff�adaptive_speed�
base_speed�combined_speed�speeds                  r   �updatezKaraokeEngine.update+   st  � � �y�y�� �i�i�/�/�1���+�%� ,�D�� $�D�� $�D��!$�D���(��� %�t�'7�'7�7�
� AE�	�	�@c�@c�dq�@r�=���4�,�o� �D�1�1�
1�� ,��� ���3�&�$�*;�*;�t�~�~�*M�#2�D� �!0�D��!�.�.�D�N�� JL�a���4�+?�+?�?�2�E�UV�
�  /��� ,�� �����!2�#'�#9�#9� � �s�"�"6��"<����$�"6��"<�� #7�� !$�C��=�?�;Z�)Z� [�� �t�!�!�$5�5�6��>� �t�)�)�,=�=�>�D� "�2�2�R�7�3����;K�L�N� �.�.��3�c�9�J� !��^�<�N� ���,�E� !�4�#5�#5�5�%(��);�);�e�)C�EV�%W��"� �$�$����6�):�D�&� !�D�$6�$6�6�%6��"� �*�*��r   �returnc                 �P   � | j                   sy| j                   j                  �       S )u.   Devuelve el texto completo de la línea actual� )r   �get_text�r   s    r   �get_current_line_textz#KaraokeEngine.get_current_line_text�   s#   � �� � ��� � �)�)�+�+r   c                 �6  � | j                   r| j                  sdd| j                  �       fS d}| j                  j                  }d}d}| j                   j                  D ]5  }|| j                  k(  rd}�|s||j                  z
  }�'||j                  z
  }�7 |||fS )u�   
        Devuelve el texto dividido en tres partes:
        1. Texto ya cantado en la línea actual
        2. Texto de la sílaba actual
        3. Texto pendiente de cantar en la línea actual
        r5   FT)r   r   r8   �text�notes)r   �	sung_text�current_syllable�pending_text�
found_current�notes         r   �get_highlighted_textz"KaraokeEngine.get_highlighted_text�   s�   � � � � ��(9�(9��r�4�5�5�7�7�7� �	��,�,�1�1�����
��%�%�+�+�D��t�(�(�(� $�
�"��T�Y�Y�&�	���	�	�)��
 ,� �*�L�8�8r   c                 �   � | j                   r| j                  syd}| j                   j                  D ](  }|r|j                  �       c S || j                  k(  s�'d}�* y)u(   Devuelve el texto de la siguiente línear5   FT)r   r   �linesr6   �r   r?   �lines      r   �get_next_line_textz KaraokeEngine.get_next_line_text�   sT   � ��y�y�� 1� 1�� �
��I�I�O�O�D���}�}��&��t�(�(�(� $�
�	 $� r   c                 �   � | j                   S )uz   
        Devuelve el progreso de la sílaba actual (0.0 - 1.0)
        Útil para efectos de resaltado progresivo
        )r   r7   s    r   �get_syllable_progressz#KaraokeEngine.get_syllable_progress�   s   � �
 �!�!�!r   c                 �  � | j                   sy| j                  j                  | j                   j                  �      | j                  j                  | j                   j                  �      z
  }|dk  ry| j
                  | j                  j                  | j                   j                  �      z
  }t
        dt        d||z  �      �      S )uF   
        Devuelve el progreso de la línea actual (0.0 - 1.0)
        r
   r   r    )r   r   �
get_beat_time�end�startr   r%   r#   )r   �
line_duration�current_positions      r   �get_line_progresszKaraokeEngine.get_line_progress�   s�   � � � � ���	�	�/�/��0A�0A�0E�0E�F����I`�I`�ae�ar�ar�ax�ax�Iy�y�
��A����,�,�t�y�y�/F�/F�t�GX�GX�G^�G^�/_�_���3��C�!1�M�!A�B�C�Cr   c                 �   � | j                   r| j                  syd}| j                   j                  D ]J  }|r4| j                   j                  |j                  �      | j
                  z
  c S || j                  k(  s�Id}�L y)uy   
        Devuelve el tiempo en segundos hasta la próxima línea
        Útil para mostrar una cuenta regresiva
        r
   FT)r   r   rC   rJ   rL   r   rD   s      r   �get_countdown_to_next_linez(KaraokeEngine.get_countdown_to_next_line�   sn   � �
 �y�y�� 1� 1�� �
��I�I�O�O�D���y�y�.�.�t�z�z�:�T�=N�=N�N�N��t�(�(�(� $�
�	 $� r   )N)�__name__�
__module__�__qualname__�__doc__r   r   r   �floatr2   �strr8   r   rA   rF   rH   rO   rQ   � r   r   r
   r
      s�   � �6�'�T� '�"$�T� $�j+�5� j+�X,�s� ,�9�e�C��c�M�&:� 9�8
�C� 
�"�u� "�D�5� D��E� r   r
   N)	�typingr   r   r   �src.core.song_loaderr   r   r   r
   rX   r   r   �<module>r[      s   �� )� (� 1� 1�e� er   
