import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"

    // Parámetros del efecto Wave (basados en el QPaint original)
    property real waveMaxScale: 1.35       // Escala máxima (del QPaint original)
    property real waveScaleWidth: 18.0     // Ancho de transición (del QPaint original)
    property real verticalStretch: 1.8     // Estiramiento vertical específico
    property real waveSpeed: 2.0           // Velocidad de animación

    // Colores del efecto de luz interior (del QPaint original)
    readonly property color innerLightColor: Qt.rgba(180/255, 1.0, 180/255, 1.0)  // Verde claro brillante
    readonly property color outerLightColor: Qt.rgba(100/255, 1.0, 100/255, 1.0)  // Verde medio

    // Propiedades seguras
    readonly property string safeSungText: root.sungText || ""
    readonly property string safeCurrentSyllable: root.currentSyllable || ""
    readonly property string safePendingText: root.pendingText || ""
    readonly property real safeSyllableProgress: Math.max(0, Math.min(1, root.syllableProgress || 0))

    // Métricas de texto
    TextMetrics {
        id: sungMetrics
        font: root.textFont
        text: root.safeSungText
    }

    TextMetrics {
        id: currentMetrics
        font: root.textFont
        text: root.safeCurrentSyllable
    }

    // Animación continua
    property real animationTime: 0.0

    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS
        onTriggered: animationTime += 0.016
    }

    // Función para calcular el factor de escala (EXACTA del QPaint original)
    function calculateWaveScale(distanceFromFront) {
        if (distanceFromFront >= waveScaleWidth * 3) {
            return 1.0
        }

        // Fórmula exacta del QPaint original
        var exponentialFactor = Math.exp(-0.3 * Math.pow(distanceFromFront / waveScaleWidth, 2))
        return 1.0 + (waveMaxScale - 1.0) * exponentialFactor
    }

    // Función para calcular el estiramiento vertical con animación
    function calculateVerticalStretch(baseScale) {
        // Añadir pulso animado al estiramiento vertical
        var pulse = 0.7 + 0.3 * Math.sin(animationTime * waveSpeed * Math.PI * 2)
        return 1.0 + (verticalStretch - 1.0) * (baseScale - 1.0) * pulse
    }

    // Contenedor principal con efecto Wave recreado
    Row {
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado con efecto de onda
        Text {
            id: sungText
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true

            // Calcular efecto de onda para el texto cantado
            property real progressPosition: sungMetrics.width + (currentMetrics.width * root.safeSyllableProgress)
            property real textCenter: sungMetrics.width / 2
            property real distanceFromFront: Math.abs(textCenter - progressPosition)
            property real waveScale: root.calculateWaveScale(distanceFromFront)
            property real verticalScale: root.calculateVerticalStretch(waveScale)

            // Aplicar estiramiento vertical específico (NO escalado uniforme)
            transform: Scale {
                xScale: 1.0                    // Mantener ancho original
                yScale: verticalScale          // Solo estirar verticalmente
                origin.x: sungText.width / 2
                origin.y: sungText.height / 2
            }
        }

        // Sílaba actual con efecto de onda y luz interior
        Item {
            width: Math.max(syllableBase.width, 1)
            height: Math.max(syllableBase.height * 2, 1) // Espacio extra para estiramiento

            // Texto base (siempre visible)
            Text {
                id: syllableBase
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
                anchors.centerIn: parent
            }

            // Progreso con clip para mostrar parte cantada
            Rectangle {
                width: syllableBase.width * root.safeSyllableProgress
                height: parent.height
                color: "transparent"
                clip: true
                anchors.centerIn: parent

                // Calcular efecto de onda para la sílaba actual
                property real progressPosition: sungMetrics.width + (currentMetrics.width * root.safeSyllableProgress)
                property real syllableCenter: sungMetrics.width + (currentMetrics.width / 2)
                property real distanceFromFront: Math.abs(syllableCenter - progressPosition)
                property real waveScale: root.calculateWaveScale(distanceFromFront)
                property real verticalScale: root.calculateVerticalStretch(waveScale)

                // Contenedor para el texto cantado con efectos
                Item {
                    width: syllableBase.width
                    height: syllableBase.height
                    anchors.centerIn: parent

                    // Aplicar estiramiento vertical específico
                    transform: Scale {
                        xScale: 1.0                           // Mantener ancho original
                        yScale: parent.verticalScale          // Solo estirar verticalmente
                        origin.x: syllableBase.width / 2
                        origin.y: syllableBase.height / 2
                    }

                    // Texto base cantado
                    Text {
                        text: syllableBase.text
                        font: syllableBase.font
                        color: root.sungColor
                        antialiasing: true
                        anchors.centerIn: parent
                    }

                    // Efecto de luz exterior (del QPaint original)
                    Text {
                        text: syllableBase.text
                        font: syllableBase.font
                        color: root.outerLightColor
                        antialiasing: true
                        opacity: 0.7
                        scale: 0.98
                        transformOrigin: Item.Center
                        anchors.centerIn: parent
                    }

                    // Efecto de luz interior (del QPaint original)
                    Text {
                        text: syllableBase.text
                        font: syllableBase.font
                        color: root.innerLightColor
                        antialiasing: true
                        opacity: 0.5
                        scale: 0.95
                        transformOrigin: Item.Center
                        anchors.centerIn: parent
                    }
                }
            }
        }

        // Texto pendiente (sin efectos)
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }

    // Debug info
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 220
        height: 90
        color: "black"
        border.color: "green"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode

        Column {
            anchors.fill: parent
            anchors.margins: 5
            spacing: 2

            Text {
                text: "Wave Effect - Vertical Stretch"
                color: "white"
                font.pixelSize: 10
                font.bold: true
            }

            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "cyan"
                font.pixelSize: 9
            }

            Text {
                text: "Max Scale: " + root.waveMaxScale + " | Width: " + root.waveScaleWidth
                color: "orange"
                font.pixelSize: 9
            }

            Text {
                text: "Vertical Stretch: " + root.verticalStretch + "x"
                color: "yellow"
                font.pixelSize: 9
            }

            Text {
                text: "Current: '" + root.safeCurrentSyllable + "'"
                color: "lightgreen"
                font.pixelSize: 9
            }
        }
    }
}