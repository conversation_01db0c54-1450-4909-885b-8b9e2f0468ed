import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"

    // Parámetros del efecto de onda con estiramiento vertical
    property real verticalStretch: 1.6     // Qué tanto se estiran las letras verticalmente
    property real waveWidth: 0.4           // Ancho de la onda (0.0 - 1.0)
    property real waveSpeed: 2.5           // Velocidad de la animación
    property real glowIntensity: 0.7       // Intensidad del brillo

    // Colores del efecto luminoso
    readonly property color glowColor: Qt.rgba(0.8, 1.0, 0.8, 1.0)
    readonly property color coreColor: Qt.rgba(1.0, 1.0, 1.0, 1.0)
    readonly property color borderColor: Qt.rgba(0.4, 0.8, 0.4, 1.0)

    // Propiedades seguras
    readonly property string safeSungText: root.sungText || ""
    readonly property string safeCurrentSyllable: root.currentSyllable || ""
    readonly property string safePendingText: root.pendingText || ""
    readonly property real safeSyllableProgress: Math.max(0, Math.min(1, root.syllableProgress || 0))

    // Animación continua
    property real animationTime: 0.0

    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS
        onTriggered: animationTime += 0.016
    }

    // Función para calcular el efecto de onda en una posición específica
    function getWaveEffect(normalizedPosition) {
        // La onda se centra en el progreso actual
        var waveCenter = root.safeSyllableProgress
        var distance = Math.abs(normalizedPosition - waveCenter)

        // Solo aplicar efecto si está dentro del ancho de la onda
        if (distance > root.waveWidth) {
            return {
                verticalScale: 1.0,
                glow: 0.0,
                brightness: 0.0
            }
        }

        // Calcular intensidad basada en distancia (más fuerte en el centro)
        var intensity = 1.0 - (distance / root.waveWidth)
        intensity = Math.pow(intensity, 1.5) // Curva más pronunciada

        // Añadir pulso animado
        var pulse = 0.6 + 0.4 * Math.sin(root.animationTime * root.waveSpeed * Math.PI * 2)
        intensity *= pulse

        return {
            verticalScale: 1.0 + (root.verticalStretch - 1.0) * intensity,
            glow: root.glowIntensity * intensity,
            brightness: intensity
        }
    }

    // Contenedor principal simplificado
    Row {
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }

        // Sílaba actual con efecto de onda
        Item {
            width: syllableBase.width
            height: syllableBase.height

            // Texto base
            Text {
                id: syllableBase
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }

            // Progreso con clip
            Rectangle {
                width: parent.width * root.safeSyllableProgress
                height: parent.height
                color: "transparent"
                clip: true

                // Texto cantado con efecto de onda
                Item {
                    width: syllableBase.width
                    height: syllableBase.height

                    // Escala dinámica basada en el progreso
                    property real waveIntensity: root.getWaveIntensity()
                    scale: 1.0 + (root.waveScale - 1.0) * waveIntensity
                    transformOrigin: Item.Center

                    // Texto base cantado
                    Text {
                        text: syllableBase.text
                        font: syllableBase.font
                        color: root.sungColor
                        antialiasing: true
                    }

                    // Efecto de brillo
                    Text {
                        text: syllableBase.text
                        font: syllableBase.font
                        color: root.glowColor
                        antialiasing: true
                        opacity: parent.waveIntensity * 0.6
                        scale: 1.1
                        transformOrigin: Item.Center
                    }

                    // Núcleo brillante
                    Text {
                        text: syllableBase.text
                        font: syllableBase.font
                        color: root.coreColor
                        antialiasing: true
                        opacity: parent.waveIntensity * 0.3
                        scale: 1.05
                        transformOrigin: Item.Center
                    }
                }
            }
        }

        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }
}