import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"

    // Parámetros del efecto de onda visual
    property real waveAmplitude: 1.4        // Qué tanto se agrandan las letras
    property real waveWidth: 0.3            // Ancho de la onda (0.0 - 1.0)
    property real waveSpeed: 2.0            // Velocidad de la animación de onda
    property real glowIntensity: 0.8        // Intensidad del brillo

    // Colores del efecto luminoso
    readonly property color waveGlow: Qt.rgba(0.8, 1.0, 0.8, 1.0)      // Verde brillante
    readonly property color waveCore: Qt.rgba(1.0, 1.0, 1.0, 1.0)      // Blanco central
    readonly property color waveBorder: Qt.rgba(0.4, 0.8, 0.4, 1.0)    // Verde suave

    // Propiedades seguras
    readonly property string safeSungText: root.sungText || ""
    readonly property string safeCurrentSyllable: root.currentSyllable || ""
    readonly property string safePendingText: root.pendingText || ""
    readonly property real safeSyllableProgress: Math.max(0, Math.min(1, root.syllableProgress || 0))

    // Timer para animación continua de la onda
    property real animationTime: 0.0

    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS
        onTriggered: {
            root.animationTime += 0.016
        }
    }

    // Función para calcular el efecto de onda en una posición específica
    function getWaveEffect(normalizedPosition) {
        // La onda se centra en el progreso actual
        var waveCenter = root.safeSyllableProgress
        var distance = Math.abs(normalizedPosition - waveCenter)

        // Solo aplicar efecto si está dentro del ancho de la onda
        if (distance > root.waveWidth) {
            return {
                scale: 1.0,
                glow: 0.0,
                brightness: 0.0
            }
        }

        // Calcular intensidad basada en distancia (más fuerte en el centro)
        var intensity = 1.0 - (distance / root.waveWidth)
        intensity = Math.pow(intensity, 2) // Curva más suave

        // Añadir animación de pulso
        var pulse = 0.5 + 0.5 * Math.sin(root.animationTime * root.waveSpeed * Math.PI * 2)
        intensity *= (0.7 + 0.3 * pulse)

        return {
            scale: 1.0 + (root.waveAmplitude - 1.0) * intensity,
            glow: root.glowIntensity * intensity,
            brightness: intensity
        }
    }

    // Contenedor principal con efecto de onda visual
    Row {
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado (sin efectos especiales)
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }

        // Sílaba actual con efecto de onda que pasa
        Item {
            width: Math.max(syllableBase.width, 1)
            height: Math.max(syllableBase.height, 1)

            // Texto base (siempre visible)
            Text {
                id: syllableBase
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
                anchors.centerIn: parent
            }

            // Crear efecto de onda carácter por carácter
            Repeater {
                model: root.safeCurrentSyllable.length

                Item {
                    property string char: root.safeCurrentSyllable.charAt ? root.safeCurrentSyllable.charAt(index) : root.safeCurrentSyllable[index] || ""
                    property real charPosition: (index + 0.5) / Math.max(root.safeCurrentSyllable.length, 1)
                    property var waveEffect: root.getWaveEffect(charPosition)
                    property bool isActive: charPosition <= root.safeSyllableProgress

                    width: charMetrics.width
                    height: syllableBase.height
                    x: {
                        var totalWidth = 0
                        for (var i = 0; i < index; i++) {
                            charMetrics.text = root.safeCurrentSyllable.charAt ? root.safeCurrentSyllable.charAt(i) : root.safeCurrentSyllable[i] || ""
                            totalWidth += charMetrics.width
                        }
                        return totalWidth
                    }

                    TextMetrics {
                        id: charMetrics
                        font: root.textFont
                        text: parent.char
                    }

                    // Solo mostrar el efecto si el carácter está activo
                    visible: isActive && char.trim() !== ""

                    // Carácter con efecto de onda
                    Item {
                        anchors.centerIn: parent
                        scale: parent.waveEffect.scale
                        transformOrigin: Item.Center

                        // Capa base del carácter cantado
                        Text {
                            text: parent.char
                            font: root.textFont
                            color: root.sungColor
                            antialiasing: true
                            anchors.centerIn: parent
                        }

                        // Efecto de brillo/onda (solo si hay intensidad)
                        Item {
                            anchors.centerIn: parent
                            visible: parent.waveEffect.glow > 0.1

                            // Brillo exterior
                            Text {
                                text: parent.parent.char
                                font: root.textFont
                                color: root.waveGlow
                                antialiasing: true
                                opacity: parent.parent.waveEffect.glow * 0.6
                                scale: 1.1
                                transformOrigin: Item.Center
                                anchors.centerIn: parent
                            }

                            // Núcleo brillante
                            Text {
                                text: parent.parent.char
                                font: root.textFont
                                color: root.waveCore
                                antialiasing: true
                                opacity: parent.parent.waveEffect.brightness * 0.4
                                scale: 1.05
                                transformOrigin: Item.Center
                                anchors.centerIn: parent
                            }

                            // Borde suave
                            Text {
                                text: parent.parent.char
                                font: root.textFont
                                color: root.waveBorder
                                antialiasing: true
                                opacity: parent.parent.waveEffect.glow * 0.3
                                scale: 1.15
                                transformOrigin: Item.Center
                                anchors.centerIn: parent
                            }
                        }
                    }
                }
            }
        }

        // Texto pendiente (sin efectos)
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }

    // Debug info (opcional)
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 200
        height: 80
        color: "black"
        border.color: "green"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode

        Column {
            anchors.fill: parent
            anchors.margins: 5
            spacing: 2

            Text {
                text: "Wave Effect"
                color: "white"
                font.pixelSize: 10
                font.bold: true
            }

            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "cyan"
                font.pixelSize: 9
            }

            Text {
                text: "Wave: " + root.waveAmplitude + "x | Width: " + root.waveWidth
                color: "orange"
                font.pixelSize: 9
            }

            Text {
                text: "Current: '" + root.safeCurrentSyllable + "'"
                color: "yellow"
                font.pixelSize: 9
            }
        }
    }
}