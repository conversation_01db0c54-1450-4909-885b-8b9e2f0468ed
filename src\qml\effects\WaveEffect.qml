import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"

    // Parámetros del efecto Wave (simple y funcional)
    property real waveScale: 1.4           // Escala vertical para el efecto de onda
    property real waveSpeed: 2.0           // Velocidad de la animación

    // Colores del efecto de luz
    readonly property color waveGlowColor: Qt.rgba(0.8, 1.0, 0.8, 1.0)  // Verde brillante
    readonly property color waveCoreColor: Qt.rgba(1.0, 1.0, 1.0, 1.0)  // Blanco central

    // Propiedades seguras (siguiendo el patrón exitoso)
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0



    // Estructura principal (siguiendo el patrón exitoso de ZoomEffect y PulseEffect)
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado (sin efectos)
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }

        // Sílaba actual con efecto Wave (siguiendo el patrón exitoso)
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height

            // Texto de fondo (color pendiente) - siempre visible como base
            Text {
                id: currentSyllableText
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }

            // Contenedor para la sílaba con efecto Wave (solo cuando hay progreso)
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress > 0.0 && root.safeSyllableProgress < 1.0

                // Fondo negro para borrar el texto base (como en QPainter)
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }

                // Sílaba con efecto Wave - PASO 1: Solo escalado vertical simple
                Text {
                    id: waveSyllable
                    anchors.centerIn: parent
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true

                    // Escalado vertical simple (hacer letras más altas)
                    transform: Scale {
                        xScale: 1.0                    // Mantener ancho original
                        yScale: root.waveScale         // Solo estirar verticalmente
                        origin.x: waveSyllable.width / 2
                        origin.y: waveSyllable.height / 2
                    }

                    // Animación suave
                    Behavior on transform {
                        PropertyAnimation {
                            duration: 100
                            easing.type: Easing.OutQuad
                        }
                    }
                }

                // Efecto de brillo simple (PASO 1: Solo un brillo básico)
                Text {
                    anchors.centerIn: parent
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.waveGlowColor
                    antialiasing: true
                    opacity: 0.6
                    scale: 1.05
                    transformOrigin: Item.Center

                    // Mismo escalado vertical que el texto principal
                    transform: Scale {
                        xScale: 1.0
                        yScale: root.waveScale
                        origin.x: width / 2
                        origin.y: height / 2
                    }
                }
            }

            // Sílaba completada (tamaño normal, color cantado)
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress >= 1.0

                // Fondo negro para borrar el texto base
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }

                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                }
            }
        }

        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }

    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }

    // Debug info (siguiendo el patrón exitoso)
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 200
        height: 60
        color: "black"
        border.color: "green"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode

        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2

            Text {
                text: "Wave Scale: " + root.waveScale.toFixed(2)
                color: "green"
                font.pixelSize: 9
            }
            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "green"
                font.pixelSize: 9
            }
            Text {
                text: "Current: '" + root.safeCurrentSyllable + "'"
                color: "green"
                font.pixelSize: 9
            }
        }
    }
}