import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"
    
    // Wave effect parameters (from original QPainter implementation)
    property real waveMaxScale: 1.35
    property real waveScaleWidth: 18.0
    
    // Safe color properties with fallbacks
    property color activeLightColor: Qt.rgba(0.7, 1.0, 0.7, 1.0)  // Bright green
    property color glowColor: Qt.rgba(0.4, 1.0, 0.4, 1.0)         // Medium green
    
    // Safe property access with explicit checks
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0
    
    // Calculate wave front position (progress point in pixels)
    property real waveFrontPosition: {
        // Use character approximation like original (14px per char)
        var sungWidth = safeSungText.length * 14
        var currentWidth = safeCurrentSyllable.length * 14
        var progressInCurrent = currentWidth * safeSyllableProgress
        
        return sungWidth + progressInCurrent
    }
    
    // Wave scale calculation function (exact replica of original)
    function calculateWaveScale(distanceFromFront) {
        if (typeof distanceFromFront !== "number" || isNaN(distanceFromFront)) {
            return 1.0
        }
        
        if (distanceFromFront < waveScaleWidth * 3) {
            var normalizedDist = distanceFromFront / waveScaleWidth
            return 1.0 + (waveMaxScale - 1.0) * Math.exp(-0.3 * normalizedDist * normalizedDist)
        }
        return 1.0
    }
    
    Row {
        id: mainTextRow
        anchors.centerIn: parent
        spacing: 0
        
        // Sung text with wave scaling
        Row {
            spacing: 0
            
            Repeater {
                model: root.safeSungText.length
                
                Text {
                    property int charIndex: index
                    property real charCenterX: charIndex * 14 + 7  // Character center position
                    property real distFromWave: Math.abs(charCenterX - root.waveFrontPosition)
                    property real waveScale: root.calculateWaveScale(distFromWave)
                    
                    text: (charIndex < root.safeSungText.length) ? root.safeSungText.charAt(charIndex) : ""
                    font: root.textFont
                    color: root.sungColor
                    antialiasing: true
                    
                    transform: Scale {
                        xScale: waveScale
                        yScale: waveScale
                        origin.x: width / 2
                        origin.y: height / 2
                    }
                    
                    Behavior on transform {
                        NumberAnimation {
                            duration: 80
                            easing.type: Easing.OutQuad
                        }
                    }
                }
            }
        }
        
        // Current syllable with wave effect and inner light
        Row {
            spacing: 0
            
            Repeater {
                model: root.safeCurrentSyllable.length
                
                Item {
                    width: Math.max(baseChar.width, 1)
                    height: Math.max(baseChar.height, 1)
                    
                    property int charIndex: index
                    property real charProgress: {
                        var syllableLen = root.safeCurrentSyllable.length
                        return syllableLen > 0 ? charIndex / syllableLen : 0
                    }
                    property bool isCharActive: charProgress <= root.safeSyllableProgress
                    property real charCenterX: {
                        var sungLen = root.safeSungText.length
                        return (sungLen + charIndex) * 14 + 7
                    }
                    property real distFromWave: Math.abs(charCenterX - root.waveFrontPosition)
                    property real waveScale: root.calculateWaveScale(distFromWave)
                    
                    // Base character (always present for sizing)
                    Text {
                        id: baseChar
                        text: (parent.charIndex < root.safeCurrentSyllable.length) ? root.safeCurrentSyllable.charAt(parent.charIndex) : ""
                        font: root.textFont
                        color: "transparent"  // Invisible but provides sizing
                        antialiasing: true
                    }
                    
                    // Pending character (not yet sung)
                    Text {
                        anchors.fill: baseChar
                        text: baseChar.text
                        font: baseChar.font
                        color: root.pendingColor
                        antialiasing: true
                        visible: !parent.isCharActive
                        
                        transform: Scale {
                            xScale: parent.waveScale
                            yScale: parent.waveScale
                            origin.x: width / 2
                            origin.y: height / 2
                        }
                    }
                    
                    // Active character with inner light effect
                    Item {
                        anchors.fill: baseChar
                        visible: parent.isCharActive
                        
                        // Main bright character
                        Text {
                            anchors.fill: parent
                            text: baseChar.text
                            font: baseChar.font
                            color: root.activeLightColor
                            antialiasing: true
                            
                            transform: Scale {
                                xScale: parent.parent.waveScale
                                yScale: parent.parent.waveScale
                                origin.x: width / 2
                                origin.y: height / 2
                            }
                        }
                        
                        // Inner glow layers (simulating QPainter's inner light effect)
                        Repeater {
                            model: 2
                            
                            Text {
                                anchors.centerIn: parent
                                text: baseChar.text
                                font: baseChar.font
                                antialiasing: true
                                
                                color: index === 0 ? root.glowColor : Qt.lighter(root.activeLightColor, 1.3)
                                opacity: index === 0 ? 0.7 : 0.4
                                
                                transform: Scale {
                                    xScale: parent.parent.parent.waveScale * (0.96 - index * 0.02)
                                    yScale: parent.parent.parent.waveScale * (0.96 - index * 0.02)
                                    origin.x: width / 2
                                    origin.y: height / 2
                                }
                            }
                        }
                    }
                    
                    Behavior on waveScale {
                        NumberAnimation {
                            duration: 80
                            easing.type: Easing.OutQuad
                        }
                    }
                }
            }
        }
        
        // Pending text with subtle wave effect
        Row {
            spacing: 0
            
            Repeater {
                model: root.safePendingText.length
                
                Text {
                    property int charIndex: index
                    property real charCenterX: {
                        var sungLen = root.safeSungText.length
                        var currentLen = root.safeCurrentSyllable.length
                        return (sungLen + currentLen + charIndex) * 14 + 7
                    }
                    property real distFromWave: Math.abs(charCenterX - root.waveFrontPosition)
                    property real subtleScale: {
                        // Very subtle wave effect for pending text
                        if (distFromWave < root.waveScaleWidth * 2) {
                            var normalizedDist = distFromWave / (root.waveScaleWidth * 2)
                            return 1.0 + (root.waveMaxScale - 1.0) * 0.1 * Math.exp(-0.5 * normalizedDist * normalizedDist)
                        }
                        return 1.0
                    }
                    
                    text: (charIndex < root.safePendingText.length) ? root.safePendingText.charAt(charIndex) : ""
                    font: root.textFont
                    color: root.pendingColor
                    opacity: 0.8
                    antialiasing: true
                    
                    transform: Scale {
                        xScale: subtleScale
                        yScale: subtleScale
                        origin.x: width / 2
                        origin.y: height / 2
                    }
                }
            }
        }
    }
    
    // Next line text
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }
    
    // Debug information (only visible in debug mode)
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 180
        height: 50
        color: "black"
        border.color: "cyan"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode
        
        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2
            
            Text {
                text: "Wave Front: " + root.waveFrontPosition.toFixed(1) + "px"
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Syllable: \"" + root.safeCurrentSyllable + "\""
                color: "cyan"
                font.pixelSize: 9
            }
        }
    }
}
