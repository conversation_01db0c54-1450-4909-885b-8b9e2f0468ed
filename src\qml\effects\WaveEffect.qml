import QtQuick
import QtQuick.Effects
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"

    // Wave effect parameters (from original QPainter implementation)
    property real waveMaxScale: 1.35
    property real waveScaleWidth: 18.0

    // Enhanced visual parameters
    property real glowRadius: 8.0
    property real glowSpread: 0.3
    property real innerGlowIntensity: 0.8
    property real animationSmoothness: 0.15
    property real characterSpacing: 0.0

    // Safe color properties with fallbacks
    property color activeLightColor: Qt.rgba(0.7, 1.0, 0.7, 1.0)  // Bright green
    property color glowColor: Qt.rgba(0.4, 1.0, 0.4, 1.0)         // Medium green
    property color innerLightColor: Qt.rgba(0.9, 1.0, 0.9, 1.0)   // Very bright green

    // Safe property access with explicit checks
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0

    // Text measurement helper for accurate character positioning
    property var textMetrics: TextMetrics {
        font: root.textFont
        text: "M" // Reference character for measurements
    }

    // Calculate average character width more accurately
    property real avgCharWidth: textMetrics.advanceWidth

    // Calculate wave front position (progress point in pixels) with improved accuracy
    property real waveFrontPosition: {
        // Calculate actual text widths using TextMetrics
        var sungWidth = 0
        for (var i = 0; i < safeSungText.length; i++) {
            textMetrics.text = safeSungText.charAt(i)
            sungWidth += textMetrics.advanceWidth
        }

        var currentWidth = 0
        for (var j = 0; j < safeCurrentSyllable.length; j++) {
            textMetrics.text = safeCurrentSyllable.charAt(j)
            currentWidth += textMetrics.advanceWidth
        }

        var progressInCurrent = currentWidth * safeSyllableProgress
        return sungWidth + progressInCurrent
    }

    // Wave scale calculation function (enhanced version of original)
    function calculateWaveScale(distanceFromFront) {
        if (typeof distanceFromFront !== "number" || isNaN(distanceFromFront)) {
            return 1.0
        }

        if (distanceFromFront < waveScaleWidth * 3) {
            var normalizedDist = distanceFromFront / waveScaleWidth
            return 1.0 + (waveMaxScale - 1.0) * Math.exp(-0.3 * normalizedDist * normalizedDist)
        }
        return 1.0
    }

    // Helper function to calculate character position accurately
    function getCharacterPosition(textString, charIndex) {
        var position = 0
        for (var i = 0; i < charIndex && i < textString.length; i++) {
            textMetrics.text = textString.charAt(i)
            position += textMetrics.advanceWidth
        }
        // Add half character width to get center position
        if (charIndex < textString.length) {
            textMetrics.text = textString.charAt(charIndex)
            position += textMetrics.advanceWidth / 2
        }
        return position
    }

    // Enhanced glow effect calculation
    function calculateGlowIntensity(waveScale) {
        return Math.min(1.0, (waveScale - 1.0) / (waveMaxScale - 1.0) * innerGlowIntensity)
    }

    Row {
        id: mainTextRow
        anchors.centerIn: parent
        spacing: 0

        // Sung text with enhanced wave scaling and glow effects
        Row {
            spacing: root.characterSpacing

            Repeater {
                model: root.safeSungText.length

                Item {
                    property int charIndex: index
                    property real charCenterX: root.getCharacterPosition(root.safeSungText, charIndex)
                    property real distFromWave: Math.abs(charCenterX - root.waveFrontPosition)
                    property real waveScale: root.calculateWaveScale(distFromWave)
                    property real glowIntensity: root.calculateGlowIntensity(waveScale)

                    width: sungChar.width
                    height: sungChar.height

                    // Main character with enhanced glow
                    Text {
                        id: sungChar
                        text: (parent.charIndex < root.safeSungText.length) ? root.safeSungText.charAt(parent.charIndex) : ""
                        font: root.textFont
                        color: root.sungColor
                        antialiasing: true

                        transform: Scale {
                            xScale: parent.waveScale
                            yScale: parent.waveScale
                            origin.x: width / 2
                            origin.y: height / 2
                        }

                        Behavior on transform {
                            NumberAnimation {
                                duration: 60 + root.animationSmoothness * 100
                                easing.type: Easing.OutQuad
                            }
                        }
                    }

                    // Enhanced glow effect using Qt 6 GraphicalEffects
                    DropShadow {
                        anchors.fill: sungChar
                        source: sungChar
                        radius: root.glowRadius * parent.glowIntensity
                        spread: root.glowSpread
                        color: root.glowColor
                        transparentBorder: true
                        cached: true

                        transform: Scale {
                            xScale: parent.waveScale
                            yScale: parent.waveScale
                            origin.x: width / 2
                            origin.y: height / 2
                        }
                    }
                }
            }
        }

        // Current syllable with enhanced wave effect and inner light
        Row {
            spacing: root.characterSpacing

            Repeater {
                model: root.safeCurrentSyllable.length

                Item {
                    width: Math.max(baseChar.width, 1)
                    height: Math.max(baseChar.height, 1)

                    property int charIndex: index
                    property real charProgress: {
                        var syllableLen = root.safeCurrentSyllable.length
                        return syllableLen > 0 ? charIndex / syllableLen : 0
                    }
                    property bool isCharActive: charProgress <= root.safeSyllableProgress
                    property real charCenterX: {
                        var sungWidth = 0
                        for (var i = 0; i < root.safeSungText.length; i++) {
                            root.textMetrics.text = root.safeSungText.charAt(i)
                            sungWidth += root.textMetrics.advanceWidth
                        }
                        return sungWidth + root.getCharacterPosition(root.safeCurrentSyllable, charIndex)
                    }
                    property real distFromWave: Math.abs(charCenterX - root.waveFrontPosition)
                    property real waveScale: root.calculateWaveScale(distFromWave)
                    property real glowIntensity: root.calculateGlowIntensity(waveScale)

                    // Base character (always present for sizing)
                    Text {
                        id: baseChar
                        text: (parent.charIndex < root.safeCurrentSyllable.length) ? root.safeCurrentSyllable.charAt(parent.charIndex) : ""
                        font: root.textFont
                        color: "transparent"  // Invisible but provides sizing
                        antialiasing: true
                    }

                    // Pending character (not yet sung) with subtle wave effect
                    Item {
                        anchors.fill: baseChar
                        visible: !parent.isCharActive

                        Text {
                            anchors.fill: parent
                            text: baseChar.text
                            font: baseChar.font
                            color: root.pendingColor
                            antialiasing: true

                            transform: Scale {
                                xScale: parent.parent.waveScale
                                yScale: parent.parent.waveScale
                                origin.x: width / 2
                                origin.y: height / 2
                            }
                        }

                        // Subtle glow for pending characters
                        DropShadow {
                            anchors.fill: parent
                            source: parent
                            radius: root.glowRadius * 0.3
                            spread: 0.1
                            color: Qt.rgba(root.pendingColor.r, root.pendingColor.g, root.pendingColor.b, 0.3)
                            transparentBorder: true
                            cached: true
                        }
                    }

                    // Active character with enhanced inner light effect
                    Item {
                        anchors.fill: baseChar
                        visible: parent.isCharActive

                        // Main bright character
                        Text {
                            id: activeChar
                            anchors.fill: parent
                            text: baseChar.text
                            font: baseChar.font
                            color: root.activeLightColor
                            antialiasing: true

                            transform: Scale {
                                xScale: parent.parent.waveScale
                                yScale: parent.parent.waveScale
                                origin.x: width / 2
                                origin.y: height / 2
                            }
                        }

                        // Enhanced multi-layer glow effect
                        DropShadow {
                            anchors.fill: activeChar
                            source: activeChar
                            radius: root.glowRadius * parent.parent.glowIntensity
                            spread: root.glowSpread
                            color: root.glowColor
                            transparentBorder: true
                            cached: true
                        }

                        // Inner bright glow
                        Glow {
                            anchors.fill: activeChar
                            source: activeChar
                            radius: root.glowRadius * 0.5 * parent.parent.glowIntensity
                            spread: 0.2
                            color: root.innerLightColor
                            transparentBorder: true
                            cached: true
                        }
                    }

                    Behavior on waveScale {
                        NumberAnimation {
                            duration: 60 + root.animationSmoothness * 100
                            easing.type: Easing.OutQuad
                        }
                    }

                    Behavior on glowIntensity {
                        NumberAnimation {
                            duration: 100
                            easing.type: Easing.OutQuad
                        }
                    }
                }
            }
        }

        // Pending text with enhanced subtle wave effect
        Row {
            spacing: root.characterSpacing

            Repeater {
                model: root.safePendingText.length

                Item {
                    property int charIndex: index
                    property real charCenterX: {
                        var sungWidth = 0
                        for (var i = 0; i < root.safeSungText.length; i++) {
                            root.textMetrics.text = root.safeSungText.charAt(i)
                            sungWidth += root.textMetrics.advanceWidth
                        }
                        var currentWidth = 0
                        for (var j = 0; j < root.safeCurrentSyllable.length; j++) {
                            root.textMetrics.text = root.safeCurrentSyllable.charAt(j)
                            currentWidth += root.textMetrics.advanceWidth
                        }
                        return sungWidth + currentWidth + root.getCharacterPosition(root.safePendingText, charIndex)
                    }
                    property real distFromWave: Math.abs(charCenterX - root.waveFrontPosition)
                    property real subtleScale: {
                        // Very subtle wave effect for pending text
                        if (distFromWave < root.waveScaleWidth * 2) {
                            var normalizedDist = distFromWave / (root.waveScaleWidth * 2)
                            return 1.0 + (root.waveMaxScale - 1.0) * 0.1 * Math.exp(-0.5 * normalizedDist * normalizedDist)
                        }
                        return 1.0
                    }

                    width: pendingChar.width
                    height: pendingChar.height

                    Text {
                        id: pendingChar
                        text: (parent.charIndex < root.safePendingText.length) ? root.safePendingText.charAt(parent.charIndex) : ""
                        font: root.textFont
                        color: root.pendingColor
                        opacity: 0.8
                        antialiasing: true

                        transform: Scale {
                            xScale: parent.subtleScale
                            yScale: parent.subtleScale
                            origin.x: width / 2
                            origin.y: height / 2
                        }

                        Behavior on transform {
                            NumberAnimation {
                                duration: 100
                                easing.type: Easing.OutQuad
                            }
                        }
                    }

                    // Very subtle glow for pending text
                    DropShadow {
                        anchors.fill: pendingChar
                        source: pendingChar
                        radius: 2
                        spread: 0.1
                        color: Qt.rgba(root.pendingColor.r, root.pendingColor.g, root.pendingColor.b, 0.2)
                        transparentBorder: true
                        cached: true
                        opacity: parent.subtleScale > 1.0 ? 0.5 : 0.0

                        Behavior on opacity {
                            NumberAnimation { duration: 200 }
                        }
                    }
                }
            }
        }
    }

    // Next line text
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }

    // Enhanced debug information (only visible in debug mode)
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 220
        height: 80
        color: "black"
        border.color: "cyan"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode

        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 1

            Text {
                text: "Wave Front: " + root.waveFrontPosition.toFixed(1) + "px"
                color: "cyan"
                font.pixelSize: 8
            }
            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "cyan"
                font.pixelSize: 8
            }
            Text {
                text: "Syllable: \"" + root.safeCurrentSyllable + "\""
                color: "cyan"
                font.pixelSize: 8
            }
            Text {
                text: "Avg Char Width: " + root.avgCharWidth.toFixed(1) + "px"
                color: "yellow"
                font.pixelSize: 8
            }
            Text {
                text: "Max Scale: " + root.waveMaxScale.toFixed(2)
                color: "yellow"
                font.pixelSize: 8
            }
            Text {
                text: "Scale Width: " + root.waveScaleWidth.toFixed(1) + "px"
                color: "yellow"
                font.pixelSize: 8
            }
            Text {
                text: "Glow Radius: " + root.glowRadius.toFixed(1) + "px"
                color: "green"
                font.pixelSize: 8
            }
        }
    }
}
