import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"

    // Parámetros del efecto Wave (PASO 2: Onda que se mueve)
    property real waveMaxScale: 1.6        // Escala máxima del efecto de onda
    property real waveWidth: 0.4           // Ancho de la onda (0.0 - 1.0)
    property real waveSpeed: 3.0           // Velocidad de la animación

    // Colores del efecto de luz (PASO 3: <PERSON><PERSON><PERSON><PERSON> capas)
    readonly property color waveGlowOuter: Qt.rgba(0.4, 0.8, 0.4, 1.0)  // Verde suave exterior
    readonly property color waveGlowMid: Qt.rgba(0.8, 1.0, 0.8, 1.0)    // Verde brillante medio
    readonly property color waveGlowInner: Qt.rgba(1.0, 1.0, 1.0, 1.0)  // Blanco central brillante

    // Animación continua para el efecto de onda
    property real animationTime: 0.0

    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS
        onTriggered: animationTime += 0.016
    }

    // Función para calcular el efecto de onda en una posición específica
    function getWaveIntensity(normalizedPosition) {
        // La onda se centra en el progreso actual con un offset animado
        var waveCenter = safeSyllableProgress + 0.1 * Math.sin(animationTime * waveSpeed)
        var distance = Math.abs(normalizedPosition - waveCenter)

        // Solo aplicar efecto si está dentro del ancho de la onda
        if (distance > waveWidth) {
            return 1.0
        }

        // Calcular intensidad basada en distancia (más fuerte en el centro)
        var intensity = 1.0 - (distance / waveWidth)
        intensity = Math.pow(intensity, 1.5) // Curva más pronunciada

        // Añadir pulso animado
        var pulse = 0.7 + 0.3 * Math.sin(animationTime * waveSpeed * 2)

        return 1.0 + (waveMaxScale - 1.0) * intensity * pulse
    }

    // Propiedades seguras (siguiendo el patrón exitoso)
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0



    // Estructura principal (siguiendo el patrón exitoso de ZoomEffect y PulseEffect)
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado (sin efectos)
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }

        // Sílaba actual con efecto Wave (siguiendo el patrón exitoso)
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height

            // Texto de fondo (color pendiente) - siempre visible como base
            Text {
                id: currentSyllableText
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }

            // Contenedor para la sílaba con efecto Wave (solo cuando hay progreso)
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress > 0.0 && root.safeSyllableProgress < 1.0

                // Fondo negro para borrar el texto base (como en QPainter)
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }

                // PASO 2: Onda que se mueve carácter por carácter
                Row {
                    anchors.centerIn: parent
                    spacing: 0

                    Repeater {
                        model: root.safeCurrentSyllable.length

                        Item {
                            property string currentChar: root.safeCurrentSyllable.length > index ? root.safeCurrentSyllable[index] : ""
                            property real charPosition: (index + 0.5) / Math.max(root.safeCurrentSyllable.length, 1)
                            property real waveIntensity: root.getWaveIntensity(charPosition)

                            width: charMetrics.width
                            height: charText.height

                            TextMetrics {
                                id: charMetrics
                                font: root.textFont
                                text: parent.currentChar
                            }

                            // Carácter base con efecto de onda
                            Text {
                                id: charText
                                text: parent.currentChar
                                font: root.textFont
                                color: root.sungColor
                                antialiasing: true
                                anchors.centerIn: parent

                                // Escalado vertical dinámico basado en la onda
                                transform: Scale {
                                    xScale: 1.0                        // Mantener ancho original
                                    yScale: parent.waveIntensity       // Escalado vertical dinámico
                                    origin.x: charText.width / 2
                                    origin.y: charText.height / 2
                                }
                            }

                            // Efecto de brillo que sigue la onda
                            Text {
                                text: parent.currentChar
                                font: root.textFont
                                color: root.waveGlowColor
                                antialiasing: true
                                opacity: Math.max(0, (parent.waveIntensity - 1.0) * 0.8) // Solo brillar cuando hay efecto
                                anchors.centerIn: parent

                                // Mismo escalado que el carácter base
                                transform: Scale {
                                    xScale: 1.0
                                    yScale: parent.waveIntensity
                                    origin.x: width / 2
                                    origin.y: height / 2
                                }
                            }
                        }
                    }
                }
            }

            // Sílaba completada (tamaño normal, color cantado)
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress >= 1.0

                // Fondo negro para borrar el texto base
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }

                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                }
            }
        }

        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }

    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }

    // Debug info (PASO 2: Información de la onda)
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 220
        height: 80
        color: "black"
        border.color: "green"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode

        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2

            Text {
                text: "Wave Effect - PASO 2"
                color: "white"
                font.pixelSize: 10
                font.bold: true
            }
            Text {
                text: "Max Scale: " + root.waveMaxScale.toFixed(2) + " | Width: " + root.waveWidth.toFixed(2)
                color: "green"
                font.pixelSize: 9
            }
            Text {
                text: "Animation Time: " + root.animationTime.toFixed(2)
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "yellow"
                font.pixelSize: 9
            }
            Text {
                text: "Current: '" + root.safeCurrentSyllable + "'"
                color: "lightgreen"
                font.pixelSize: 9
            }
        }
    }
}