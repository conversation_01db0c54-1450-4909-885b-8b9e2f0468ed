import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"

    // Parámetros del efecto Wave (basados en el QPaint original)
    property real waveMaxScale: 1.35       // Escala máxima (del QPaint original)
    property real waveScaleWidth: 18.0     // Ancho de transición (del QPaint original)

    // Colores del efecto de luz interior (del QPaint original)
    readonly property color innerLightColor: Qt.rgba(180/255, 1.0, 180/255, 1.0)  // Verde claro brillante
    readonly property color outerLightColor: Qt.rgba(100/255, 1.0, 100/255, 1.0)  // Verde medio

    // Propiedades seguras
    readonly property string safeSungText: root.sungText || ""
    readonly property string safeCurrentSyllable: root.currentSyllable || ""
    readonly property string safePendingText: root.pendingText || ""
    readonly property real safeSyllableProgress: Math.max(0, Math.min(1, root.syllableProgress || 0))

    // Métricas de texto
    TextMetrics {
        id: sungMetrics
        font: root.textFont
        text: root.safeSungText
    }

    TextMetrics {
        id: currentMetrics
        font: root.textFont
        text: root.safeCurrentSyllable
    }



    // Función para calcular el factor de escala (EXACTA del QPaint original)
    function calculateWaveScale(distanceFromFront) {
        if (distanceFromFront >= waveScaleWidth * 3) {
            return 1.0
        }

        // Fórmula exacta del QPaint original
        var exponentialFactor = Math.exp(-0.3 * Math.pow(distanceFromFront / waveScaleWidth, 2))
        return 1.0 + (waveMaxScale - 1.0) * exponentialFactor
    }



    // Contenedor principal con efecto Wave carácter por carácter
    Row {
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado con efecto de onda carácter por carácter
        Repeater {
            model: root.safeSungText.length

            Item {
                property string currentChar: root.safeSungText.length > index ? root.safeSungText[index] : ""
                property real charWidth: charMetrics.width
                property real charCenter: {
                    var totalWidth = 0
                    for (var i = 0; i <= index; i++) {
                        charMetrics.text = root.safeSungText.length > i ? root.safeSungText[i] : ""
                        if (i < index) {
                            totalWidth += charMetrics.width
                        } else {
                            totalWidth += charMetrics.width / 2
                        }
                    }
                    return totalWidth
                }
                property real progressPosition: sungMetrics.width + (currentMetrics.width * root.safeSyllableProgress)
                property real distanceFromFront: Math.abs(charCenter - progressPosition)
                property real waveScale: root.calculateWaveScale(distanceFromFront)

                width: charWidth
                height: charText.height

                TextMetrics {
                    id: charMetrics
                    font: root.textFont
                    text: parent.currentChar
                }

                Text {
                    id: charText
                    text: parent.currentChar
                    font: root.textFont
                    color: root.sungColor
                    antialiasing: true
                    anchors.centerIn: parent

                    // Aplicar SOLO escalado vertical (hacer letras más altas)
                    transform: Scale {
                        xScale: 1.0                    // Mantener ancho original
                        yScale: parent.waveScale       // Solo estirar verticalmente
                        origin.x: charText.width / 2
                        origin.y: charText.height / 2
                    }
                }
            }
        }

        // Sílaba actual con efecto de onda carácter por carácter
        Repeater {
            model: root.safeCurrentSyllable.length

            Item {
                property string currentChar: root.safeCurrentSyllable.length > index ? root.safeCurrentSyllable[index] : ""
                property real charWidth: currentCharMetrics.width
                property real charCenter: {
                    var totalWidth = sungMetrics.width
                    for (var i = 0; i <= index; i++) {
                        currentCharMetrics.text = root.safeCurrentSyllable.length > i ? root.safeCurrentSyllable[i] : ""
                        if (i < index) {
                            totalWidth += currentCharMetrics.width
                        } else {
                            totalWidth += currentCharMetrics.width / 2
                        }
                    }
                    return totalWidth
                }
                property real progressPosition: sungMetrics.width + (currentMetrics.width * root.safeSyllableProgress)
                property real distanceFromFront: Math.abs(charCenter - progressPosition)
                property real waveScale: root.calculateWaveScale(distanceFromFront)
                property real charProgress: {
                    var charStart = sungMetrics.width
                    for (var i = 0; i < index; i++) {
                        currentCharMetrics.text = root.safeCurrentSyllable.length > i ? root.safeCurrentSyllable[i] : ""
                        charStart += currentCharMetrics.width
                    }
                    return Math.max(0, Math.min(1, (progressPosition - charStart) / charWidth))
                }
                property bool isKaraoke: charProgress > 0

                width: charWidth
                height: Math.max(currentCharText.height * 2, 1) // Espacio extra para estiramiento

                TextMetrics {
                    id: currentCharMetrics
                    font: root.textFont
                    text: parent.currentChar
                }

                // Texto base (siempre visible)
                Text {
                    id: currentCharText
                    text: parent.currentChar
                    font: root.textFont
                    color: root.pendingColor
                    antialiasing: true
                    anchors.centerIn: parent

                    // Aplicar SOLO escalado vertical
                    transform: Scale {
                        xScale: 1.0                    // Mantener ancho original
                        yScale: parent.waveScale       // Solo estirar verticalmente
                        origin.x: currentCharText.width / 2
                        origin.y: currentCharText.height / 2
                    }
                }

                // Progreso con clip para mostrar parte cantada
                Rectangle {
                    width: parent.width * parent.charProgress
                    height: parent.height
                    color: "transparent"
                    clip: true
                    anchors.centerIn: parent

                    // Contenedor para el texto cantado con efectos
                    Item {
                        width: currentCharText.width
                        height: currentCharText.height
                        anchors.centerIn: parent

                        // Aplicar SOLO escalado vertical
                        transform: Scale {
                            xScale: 1.0                           // Mantener ancho original
                            yScale: parent.parent.waveScale      // Solo estirar verticalmente
                            origin.x: currentCharText.width / 2
                            origin.y: currentCharText.height / 2
                        }

                        // Texto base cantado
                        Text {
                            text: currentCharText.text
                            font: currentCharText.font
                            color: root.sungColor
                            antialiasing: true
                            anchors.centerIn: parent
                        }

                        // Efecto de luz exterior (del QPaint original)
                        Text {
                            text: currentCharText.text
                            font: currentCharText.font
                            color: root.outerLightColor
                            antialiasing: true
                            opacity: 0.7
                            scale: 0.98
                            transformOrigin: Item.Center
                            anchors.centerIn: parent
                        }

                        // Efecto de luz interior (del QPaint original)
                        Text {
                            text: currentCharText.text
                            font: currentCharText.font
                            color: root.innerLightColor
                            antialiasing: true
                            opacity: 0.5
                            scale: 0.95
                            transformOrigin: Item.Center
                            anchors.centerIn: parent
                        }
                    }
                }
            }
        }

        // Texto pendiente (sin efectos)
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }

    // Debug info
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 220
        height: 90
        color: "black"
        border.color: "green"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode

        Column {
            anchors.fill: parent
            anchors.margins: 5
            spacing: 2

            Text {
                text: "Wave Effect - Vertical Stretch"
                color: "white"
                font.pixelSize: 10
                font.bold: true
            }

            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "cyan"
                font.pixelSize: 9
            }

            Text {
                text: "Max Scale: " + root.waveMaxScale + " | Width: " + root.waveScaleWidth
                color: "orange"
                font.pixelSize: 9
            }

            Text {
                text: "Character-by-character scaling"
                color: "yellow"
                font.pixelSize: 9
            }

            Text {
                text: "Current: '" + root.safeCurrentSyllable + "'"
                color: "lightgreen"
                font.pixelSize: 9
            }
        }
    }
}