import QtQuick 2.15
import QtGraphicalEffects 1.15
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"

    // Parámetros exactos del QPainter original
    property real waveMaxScale: 1.35        // Escala máxima para el efecto de onda
    property real waveScaleWidth: 18.0      // Ancho de la transición de escala
    property real waveExponent: 0.3         // Exponente para la función de escala

    // Colores para el efecto de luz interior (exactos del QPainter original)
    readonly property color innerLightColor: Qt.rgba(180/255, 1.0, 180/255, 1.0)  // Verde claro brillante
    readonly property color outerLightColor: Qt.rgba(100/255, 1.0, 100/255, 1.0)  // Verde medio
    readonly property color baseColor: root.sungColor                               // Color base

    // Propiedades seguras
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0

    // Métricas de texto para cálculos precisos
    TextMetrics {
        id: sungTextMetrics
        font: root.textFont
        text: root.safeSungText
    }

    TextMetrics {
        id: currentSyllableMetrics
        font: root.textFont
        text: root.safeCurrentSyllable
    }

    // Función para calcular el factor de escala basado en la distancia (EXACTA del QPainter original)
    function calculateScaleFactor(distanceFromFront) {
        if (distanceFromFront >= root.waveScaleWidth * 3) {
            return 1.0
        }

        var normalizedDistance = distanceFromFront / root.waveScaleWidth
        var exponentialFactor = Math.exp(-root.waveExponent * normalizedDistance * normalizedDistance)
        return 1.0 + (root.waveMaxScale - 1.0) * exponentialFactor
    }

    // Función para obtener color más claro (igual que en QPainter)
    function getLighterColor(baseColor, lightnessFactor) {
        var r = baseColor.r
        var g = baseColor.g
        var b = baseColor.b

        var newR = r + (1.0 - r) * lightnessFactor
        var newG = g + (1.0 - g) * lightnessFactor
        var newB = b + (1.0 - b) * lightnessFactor

        return Qt.rgba(newR, newG, newB, baseColor.a)
    }

    // Contenedor principal con enfoque simplificado
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado (con efecto de escala)
        Text {
            id: sungText
            text: root.safeSungText
            font: root.textFont
            color: root.baseColor
            antialiasing: true

            // Aplicar efecto de escala basado en la distancia al progreso
            property real progressPosition: sungTextMetrics.width + (currentSyllableMetrics.width * root.safeSyllableProgress)
            property real textCenter: sungTextMetrics.width / 2
            property real distanceFromFront: Math.abs(textCenter - progressPosition)
            property real scaleFactor: root.calculateScaleFactor(distanceFromFront)

            scale: scaleFactor
            transformOrigin: Item.Center
        }

        // Sílaba actual con efecto de progreso y luz interior
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height

            property real progressPosition: sungTextMetrics.width + (currentSyllableMetrics.width * root.safeSyllableProgress)
            property real syllableCenter: sungTextMetrics.width + (currentSyllableMetrics.width / 2)
            property real distanceFromFront: Math.abs(syllableCenter - progressPosition)
            property real scaleFactor: root.calculateScaleFactor(distanceFromFront)

            // Texto base (color pendiente)
            Text {
                id: currentSyllableText
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
                scale: parent.scaleFactor
                transformOrigin: Item.Center
            }

            // Máscara para el progreso (parte cantada)
            Rectangle {
                width: parent.width * root.safeSyllableProgress
                height: parent.height
                color: "transparent"
                clip: true

                // Texto cantado con efecto de luz interior
                Item {
                    width: currentSyllableText.width
                    height: currentSyllableText.height
                    scale: parent.parent.scaleFactor
                    transformOrigin: Item.Center

                    // Capa base (color cantado)
                    Text {
                        text: currentSyllableText.text
                        font: currentSyllableText.font
                        color: root.baseColor
                        antialiasing: true
                    }

                    // Capa de luz exterior
                    Text {
                        text: currentSyllableText.text
                        font: currentSyllableText.font
                        color: root.outerLightColor
                        antialiasing: true
                        opacity: 0.7
                        scale: 0.98
                        transformOrigin: Item.Center
                    }

                    // Capa de luz interior (más brillante)
                    Text {
                        text: currentSyllableText.text
                        font: currentSyllableText.font
                        color: root.innerLightColor
                        antialiasing: true
                        opacity: 0.5
                        scale: 0.95
                        transformOrigin: Item.Center
                    }
                }
            }
        }

        // Texto pendiente (sin efectos)
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }

    // Debug info (opcional)
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 250
        height: 100
        color: "black"
        border.color: "green"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode

        Column {
            anchors.fill: parent
            anchors.margins: 5
            spacing: 2

            Text {
                text: "Wave Effect Debug"
                color: "white"
                font.pixelSize: 10
                font.bold: true
            }

            Text {
                text: "Sung: '" + root.safeSungText + "'"
                color: "lightgreen"
                font.pixelSize: 9
            }

            Text {
                text: "Current: '" + root.safeCurrentSyllable + "'"
                color: "yellow"
                font.pixelSize: 9
            }

            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "cyan"
                font.pixelSize: 9
            }

            Text {
                text: "Scale: " + root.waveMaxScale + " | Width: " + root.waveScaleWidth
                color: "orange"
                font.pixelSize: 9
            }
        }
    }
}