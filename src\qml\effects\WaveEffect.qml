import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "WaveEffect"

    // Parámetros del efecto de onda
    property real waveScale: 1.5
    property real waveSpeed: 3.0
    property real glowOpacity: 0.8

    // Colores del efecto
    readonly property color glowColor: Qt.rgba(0.8, 1.0, 0.8, 1.0)
    readonly property color coreColor: Qt.rgba(1.0, 1.0, 1.0, 1.0)

    // Propiedades seguras
    readonly property string safeSungText: root.sungText || ""
    readonly property string safeCurrentSyllable: root.currentSyllable || ""
    readonly property string safePendingText: root.pendingText || ""
    readonly property real safeSyllableProgress: Math.max(0, Math.min(1, root.syllableProgress || 0))

    // Animación
    property real time: 0.0

    Timer {
        running: true
        repeat: true
        interval: 16
        onTriggered: time += 0.016
    }

    // Función simple para calcular el efecto
    function getWaveIntensity() {
        var pulse = 0.5 + 0.5 * Math.sin(time * waveSpeed)
        return pulse * glowOpacity
    }

    // Contenedor principal simplificado
    Row {
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }

        // Sílaba actual con efecto de onda
        Item {
            width: syllableBase.width
            height: syllableBase.height

            // Texto base
            Text {
                id: syllableBase
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }

            // Progreso con clip
            Rectangle {
                width: parent.width * root.safeSyllableProgress
                height: parent.height
                color: "transparent"
                clip: true

                // Texto cantado con efecto de onda
                Item {
                    width: syllableBase.width
                    height: syllableBase.height

                    // Escala dinámica basada en el progreso
                    property real waveIntensity: root.getWaveIntensity()
                    scale: 1.0 + (root.waveScale - 1.0) * waveIntensity
                    transformOrigin: Item.Center

                    // Texto base cantado
                    Text {
                        text: syllableBase.text
                        font: syllableBase.font
                        color: root.sungColor
                        antialiasing: true
                    }

                    // Efecto de brillo
                    Text {
                        text: syllableBase.text
                        font: syllableBase.font
                        color: root.glowColor
                        antialiasing: true
                        opacity: parent.waveIntensity * 0.6
                        scale: 1.1
                        transformOrigin: Item.Center
                    }

                    // Núcleo brillante
                    Text {
                        text: syllableBase.text
                        font: syllableBase.font
                        color: root.coreColor
                        antialiasing: true
                        opacity: parent.waveIntensity * 0.3
                        scale: 1.05
                        transformOrigin: Item.Center
                    }
                }
            }
        }

        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }
}