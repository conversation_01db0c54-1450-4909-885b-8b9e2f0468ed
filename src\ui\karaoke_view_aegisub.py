def _draw_aegisub_effect(self, painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text):
    """Dibuja el efecto estilo Aegisub con transformaciones 3D inspirado en el archivo .ass"""
    # Obtener métricas de la fuente
    font_metrics = QFontMetrics(self.font_current)
    
    # Calcular el ancho total del texto y el progreso actual
    sung_text_width = font_metrics.horizontalAdvance(sung_text)
    current_syllable_width = font_metrics.horizontalAdvance(current_syllable)
    
    # Posición inicial
    current_x = x
    
    # 1. Dibujar el texto ya cantado con efecto 3D
    for idx, char in enumerate(sung_text):
        char_width = font_metrics.horizontalAdvance(char)
        
        # Aplicar transformaciones 3D aleatorias pero estables para cada carácter
        # Usamos el índice del carácter como semilla para el generador de números aleatorios
        random.seed(idx * 100)
        
        # Rotación aleatoria
        rotation_x = random.uniform(-self.aegisub_rotation_max/4, self.aegisub_rotation_max/4)
        rotation_y = random.uniform(-self.aegisub_rotation_max/4, self.aegisub_rotation_max/4)
        rotation_z = random.uniform(-self.aegisub_rotation_max/4, self.aegisub_rotation_max/4)
        
        # Escala aleatoria
        scale_x = random.uniform(0.95, 1.05)
        scale_y = random.uniform(0.95, 1.05)
        
        # Color aleatorio
        color_idx = idx % len(self.aegisub_colors)
        char_color = self.aegisub_colors[color_idx]
        
        # Dibujar el carácter con transformaciones
        self._draw_aegisub_char(painter, current_x, y, char, char_color, 
                               rotation_x, rotation_y, rotation_z, 
                               scale_x, scale_y, 1.0)  # Opacidad completa
        
        # Avanzar a la siguiente posición
        current_x += char_width
    
    # 2. Dibujar la sílaba actual con efectos más intensos
    if current_syllable:
        # Dividir la sílaba actual en caracteres
        for idx, char in enumerate(current_syllable):
            char_width = font_metrics.horizontalAdvance(char)
            
            # Determinar si este carácter está bajo el progreso
            char_progress = (idx / len(current_syllable)) if len(current_syllable) > 0 else 0
            is_karaoke = char_progress <= syllable_progress
            
            if is_karaoke:
                # Para caracteres ya cantados en la sílaba actual, aplicar efectos más intensos
                # Usamos el índice y el tiempo para crear efectos dinámicos
                random.seed(idx * 100 + int(syllable_progress * 1000))
                
                # Rotación más intensa y animada
                rotation_factor = math.sin(syllable_progress * math.pi * self.aegisub_transform_speed)
                rotation_x = random.uniform(-self.aegisub_rotation_max, self.aegisub_rotation_max) * rotation_factor
                rotation_y = random.uniform(-self.aegisub_rotation_max, self.aegisub_rotation_max) * rotation_factor
                rotation_z = random.uniform(-self.aegisub_rotation_max, self.aegisub_rotation_max) * rotation_factor
                
                # Escala pulsante
                scale_factor = 1.0 + 0.2 * math.sin(syllable_progress * math.pi * 2 * self.aegisub_transform_speed)
                scale_x = scale_factor * random.uniform(self.aegisub_scale_min, self.aegisub_scale_max)
                scale_y = scale_factor * random.uniform(self.aegisub_scale_min, self.aegisub_scale_max)
                
                # Color brillante y cambiante
                color_idx = (idx + int(syllable_progress * 10)) % len(self.aegisub_colors)
                char_color = self.aegisub_colors[color_idx]
                
                # Hacer el color más brillante
                char_color = QColor(
                    min(255, char_color.red() + 50),
                    min(255, char_color.green() + 50),
                    min(255, char_color.blue() + 50)
                )
                
                # Dibujar el carácter con transformaciones intensas
                self._draw_aegisub_char(painter, current_x, y, char, char_color, 
                                       rotation_x, rotation_y, rotation_z, 
                                       scale_x, scale_y, 1.0)
            else:
                # Para caracteres pendientes en la sílaba actual, usar el color pendiente
                self._draw_aegisub_char(painter, current_x, y, char, self.color_pending, 
                                       0, 0, 0, 1.0, 1.0, 1.0)
            
            # Avanzar a la siguiente posición
            current_x += char_width
    
    # 3. Dibujar el texto pendiente
    if pending_text:
        # Dibujar el texto pendiente sin efectos
        painter.setPen(Qt.NoPen)
        painter.setBrush(self.color_pending)
        pending_path = QPainterPath()
        pending_path.addText(current_x, y, self.font_current, pending_text)
        painter.drawPath(pending_path)

def _draw_aegisub_char(self, painter, x, y, char, color, rot_x, rot_y, rot_z, scale_x, scale_y, opacity):
    """Dibuja un carácter con transformaciones 3D estilo Aegisub"""
    # Guardar el estado actual
    painter.save()
    
    # Crear un path para el carácter
    char_path = QPainterPath()
    char_width = QFontMetrics(self.font_current).horizontalAdvance(char)
    
    # Calcular el centro del carácter para las transformaciones
    char_center_x = x + char_width / 2
    char_center_y = y
    
    # Aplicar transformaciones
    painter.translate(char_center_x, char_center_y)
    
    # Aplicar rotaciones (simulación 3D)
    # Nota: PyQt5 no tiene transformaciones 3D reales, así que simulamos el efecto
    # Rotación en X (inclinación vertical)
    if rot_x != 0:
        # Simular rotación en X ajustando la escala vertical
        scale_y_factor = math.cos(math.radians(rot_x))
        painter.scale(1.0, scale_y_factor)
    
    # Rotación en Y (inclinación horizontal)
    if rot_y != 0:
        # Simular rotación en Y ajustando la escala horizontal
        scale_x_factor = math.cos(math.radians(rot_y))
        painter.scale(scale_x_factor, 1.0)
    
    # Rotación en Z (rotación en el plano)
    if rot_z != 0:
        painter.rotate(rot_z)
    
    # Aplicar escala
    painter.scale(scale_x, scale_y)
    
    # Volver al origen para dibujar el carácter
    painter.translate(-char_center_x, -char_center_y)
    
    # Establecer la opacidad
    painter.setOpacity(opacity)
    
    # Dibujar el carácter
    char_path.addText(x, y, self.font_current, char)
    
    # Aplicar efecto de desenfoque si es necesario
    if self.aegisub_blur_radius > 0:
        # Dibujar múltiples copias ligeramente desplazadas para simular desenfoque
        blur_steps = 5
        blur_opacity = 0.3 / blur_steps
        
        for i in range(blur_steps):
            # Calcular desplazamiento aleatorio
            offset_x = random.uniform(-self.aegisub_blur_radius, self.aegisub_blur_radius)
            offset_y = random.uniform(-self.aegisub_blur_radius, self.aegisub_blur_radius)
            
            # Guardar estado
            painter.save()
            
            # Aplicar desplazamiento
            painter.translate(offset_x, offset_y)
            
            # Dibujar con baja opacidad
            painter.setOpacity(blur_opacity)
            painter.setPen(Qt.NoPen)
            painter.setBrush(color)
            painter.drawPath(char_path)
            
            # Restaurar estado
            painter.restore()
    
    # Dibujar el carácter principal
    painter.setPen(Qt.NoPen)
    painter.setBrush(color)
    painter.drawPath(char_path)
    
    # Restaurar el estado
    painter.restore()
