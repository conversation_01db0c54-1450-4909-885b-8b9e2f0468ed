import QtQuick
import QtQuick.Effects

Rectangle {
    width: 200
    height: 100
    color: "black"

    Text {
        id: testText
        anchors.centerIn: parent
        text: "Test"
        color: "white"
        font.pixelSize: 24
    }

    DropShadow {
        anchors.fill: testText
        source: testText
        radius: 8
        color: "blue"
    }

    Component.onCompleted: {
        console.log("GraphicalEffects loaded successfully!")
    }
}
