import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "ShiftEffect"
    
    // Parámetros del efecto shift (exactos del QPainter original)
    property real shiftAmount: 8
    
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0
        
        // Texto cantado (sin efectos)
        Text {
            text: root.sungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }
        
        // Sílaba actual con efecto shift
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height
            
            // Texto de fondo (color pendiente) - siempre visible como base
            Text {
                id: currentSyllableText
                text: root.currentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }
            
            // Contenedor para la sílaba con shift (solo cuando hay progreso)
            Item {
                anchors.fill: parent
                visible: root.syllableProgress > 0.0 && root.syllableProgress < 1.0
                
                // Fondo negro para borrar el texto base (como en QPainter)
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }
                
                // Sílaba con desplazamiento vertical (color cantado)
                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                    
                    // Cálculo exacto del desplazamiento (igual que en QPainter)
                    // Máximo desplazamiento al principio, disminuyendo a medida que avanza
                    y: {
                        if (root.syllableProgress >= 1.0 || root.syllableProgress <= 0.0) {
                            return 0
                        }
                        // Usar función sinusoidal para suavizar el movimiento
                        return -root.shiftAmount * Math.sin((1.0 - root.syllableProgress) * Math.PI)
                    }
                    
                    Behavior on y {
                        NumberAnimation {
                            duration: 50 // Transición rápida como en el original
                            easing.type: Easing.OutQuad
                        }
                    }
                }
            }
            
            // Sílaba completada (posición normal, color cantado)
            Item {
                anchors.fill: parent
                visible: root.syllableProgress >= 1.0
                
                // Fondo negro para borrar el texto base
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }
                
                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                }
            }
        }
        
        // Texto pendiente
        Text {
            text: root.pendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }
    
    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.nextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }
}
