#!/usr/bin/env python
# -*- coding: utf-8 -*-

import math
import random
from PyQt5.QtWidgets import QWidget
from PyQt5.QtCore import Qt, QTimer, QRect, QPointF, QPoint
from PyQt5.QtGui import QPainter, QColor, QFont, QFontMetrics, QLinearGradient, QRadialGradient, QPainterPath, QPen, QImage

from src.core.karaoke_engine import KaraokeEngine
from src.core.song_loader import Song


class KaraokeView(QWidget):
    """Widget para mostrar letras de karaoke con resaltado"""

    # Definir los tipos de efectos disponibles
    EFFECT_SIMPLE = 0  # Efecto básico de resaltado de izquierda a derecha
    EFFECT_ZOOM = 1    # La sílaba actual se agranda
    EFFECT_PARTICLE = 2  # Efecto de partículas que emergen de las letras
    EFFECT_BALL = 3    # Una bola rebota sobre las sílabas
    EFFECT_SHIFT = 4   # La sílaba actual se desplaza hacia arriba
    EFFECT_WAVE = 5    # Efecto de onda con agrandamiento sutil
    EFFECT_PULSE = 6   # Efecto de pulso que hace latir las letras
    EFFECT_TYPEWRITER = 7  # Efecto de máquina de escribir con aparición gradual
    EFFECT_AEGISUB = 8  # Efecto estilo Aegisub con transformaciones 3D

    def __init__(self, parent=None):
        super().__init__(parent)

        # Configuración
        self.setMinimumSize(600, 200)
        self.setAttribute(Qt.WA_StyledBackground, True)
        self.setStyleSheet("background-color: black;")

        # Activar doble buffer para evitar parpadeos
        self.setAttribute(Qt.WA_OpaquePaintEvent)
        self.setAttribute(Qt.WA_NoSystemBackground)

        # Motor de karaoke
        self.engine = KaraokeEngine()

        # Colores
        self.color_sung = QColor(0, 255, 0)  # Verde para texto ya cantado
        self.color_pending = QColor(255, 255, 255)  # Blanco para texto pendiente
        self.color_next_line = QColor(128, 128, 128)  # Gris para la siguiente línea
        self.color_ball = QColor(255, 255, 0)  # Amarillo para la bola

        # Opciones de visualización
        self.show_transition_effect = True  # Mostrar efecto de transición suave
        self.current_effect = self.EFFECT_SIMPLE  # Efecto actual

        # Parámetros de efectos
        self.zoom_factor = 1.5  # Factor de zoom para el efecto ZOOM
        self.shift_amount = 8   # Cantidad de desplazamiento para el efecto SHIFT

        # Parámetros mejorados para el efecto BALL (estilo UltraStar WorldParty)
        self.ball_size = 18     # Tamaño base de la bola
        self.ball_offset = 60   # Distancia vertical entre la bola y las letras (aumentada significativamente)
        self.ball_smoothness = 0.8  # Factor de suavidad para el movimiento (0-1)
        self.ball_squash = 0.25  # Factor de aplastamiento cuando la bola rebota
        self.ball_shadow_offset = 5  # Desplazamiento de la sombra
        self.ball_shadow_alpha = 120  # Transparencia de la sombra (0-255)
        self.ball_highlight_size = 0.4  # Tamaño del brillo como proporción del tamaño de la bola
        self.ball_always_visible = True  # Asegurar que la bola siempre sea visible

        # Parámetros para el efecto WAVE
        self.wave_amplitude = 5.0  # Amplitud de la onda
        self.wave_length = 24.0    # Longitud de onda (en píxeles)
        self.wave_speed = 0.05     # Velocidad de la onda
        self.wave_max_scale = 1.35 # Escala máxima para el efecto de onda (aumentada para exagerar la deformidad)
        self.wave_scale_width = 18.0 # Ancho de la transición de escala (reducido para concentrar el efecto)

        # Colores para el efecto de luz interior
        self.inner_light_color = QColor(180, 255, 180)  # Color más claro y brillante (verde claro)
        self.outer_light_color = QColor(100, 255, 100)  # Color intermedio (verde medio)
        self.base_color = self.color_sung  # Color base (verde normal)

        # Parámetros para el efecto PULSE
        self.pulse_min_scale = 0.85  # Escala mínima durante el pulso
        self.pulse_max_scale = 1.25  # Escala máxima durante el pulso
        self.pulse_speed = 3.0  # Velocidad del pulso

        # Parámetros para el efecto TYPEWRITER
        self.typewriter_char_delay = 0.1  # Retraso entre caracteres
        self.typewriter_fade_in = 0.2  # Duración del fade in para cada carácter

        # Parámetros para el efecto AEGISUB (estilo Aegisub 3D)
        self.aegisub_rotation_max = 15.0  # Rotación máxima en grados (reducida para suavizar)
        self.aegisub_scale_min = 0.9  # Escala mínima (aumentada para suavizar)
        self.aegisub_scale_max = 1.2  # Escala máxima (reducida para suavizar)

        # Colores base para el efecto Aegisub (inicialmente se usan los colores generales)
        self.aegisub_color_sung = self.color_sung  # Color para texto ya cantado
        # El color activo se generará automáticamente como una versión más clara del color base
        # Para el color pendiente, usar una versión más oscura del color pendiente general
        self.aegisub_color_pending = QColor(
            max(0, self.color_pending.red() - 50),
            max(0, self.color_pending.green() - 50),
            max(0, self.color_pending.blue() - 50),
            self.color_pending.alpha()
        )

        # Factor de aclarado para el color activo (0-1, donde 1 es blanco)
        self.aegisub_active_lightness = 0.4

        # Optimización: Pre-calcular colores para evitar cálculos en tiempo real
        self.aegisub_active_color_cache = {}  # Cache de colores activos

        # Colores para efectos adicionales
        self.aegisub_colors = [
            QColor(255, 100, 100),  # Rojo claro
            QColor(100, 255, 100),  # Verde claro
            QColor(100, 100, 255),  # Azul claro
            QColor(255, 255, 100)   # Amarillo
        ]
        self.aegisub_blur_radius = 1.0  # Radio de desenfoque (reducido para optimizar)
        self.aegisub_transform_speed = 1.5  # Velocidad de las transformaciones (reducida para suavizar)

        # Parámetros de optimización
        self.aegisub_animation_smoothness = 0.8  # Factor de suavizado para animaciones (0-1)
        self.aegisub_max_blur_steps = 3  # Número máximo de pasos para el desenfoque (reducido)
        self.aegisub_bold_scale_factor = 1.15  # Factor de escala para negrita (reducido para suavizar)

        # Parámetros para el efecto PARTICLE
        self.particle_count = 15  # Número de partículas
        self.particle_size_min = 2  # Tamaño mínimo de partículas
        self.particle_size_max = 5  # Tamaño máximo de partículas
        self.particle_speed = 2.0  # Velocidad de las partículas
        self.particle_life = 0.8  # Duración de vida de las partículas
        self.particle_colors = [
            QColor(0, 255, 0),    # Verde
            QColor(100, 255, 100),  # Verde claro
            QColor(200, 255, 200)  # Verde muy claro
        ]



        # Fuentes
        self.font_current = QFont("Arial", 24, QFont.Bold)
        self.font_next = QFont("Arial", 18)

        # Almacenar el último estado para evitar actualizaciones innecesarias
        self.last_position = 0.0
        self.last_line = None
        self.last_note = None
        self.last_progress = 0.0

        # Timer para actualizar la vista
        self.update_timer = QTimer()
        self.update_timer.setInterval(10)  # ~100 FPS para reducir el desfase
        self.update_timer.timeout.connect(self.update)
        self.update_timer.start()

        # Optimizar el renderizado
        self.setUpdatesEnabled(True)  # Asegurar que las actualizaciones están habilitadas
        self.setAutoFillBackground(False)  # Desactivar el relleno automático del fondo

    def set_song(self, song: Song):
        """Establece la canción actual"""
        self.engine.set_song(song)
        self.update()

    def set_effect(self, effect_type: int):
        """Cambia el tipo de efecto de karaoke"""
        if effect_type >= self.EFFECT_SIMPLE and effect_type <= self.EFFECT_AEGISUB:
            self.current_effect = effect_type
            self.update()

    def get_effect_name(self) -> str:
        """Devuelve el nombre del efecto actual"""
        effect_names = [
            "Simple", "Zoom", "Particle", "Ball", "Shift", "Wave", "Pulse", "Typewriter", "Aegisub 3D"
        ]
        return effect_names[self.current_effect]

    def set_color(self, color_type, color):
        """Cambia uno de los colores del karaoke"""
        if color_type == "sung":
            self.color_sung = color
            # Actualizar también el color base para el efecto Aegisub
            self.aegisub_color_sung = color
            # Limpiar el caché de colores activos cuando cambia el color base
            self.aegisub_active_color_cache.clear()

            # Actualizar los colores de las partículas basados en el color base
            self.particle_colors = [
                color,  # Color base
                self._get_lighter_color(color, 0.3),  # Color más claro
                self._get_lighter_color(color, 0.6)   # Color muy claro
            ]
        elif color_type == "current":
            self.color_current = color
        elif color_type == "pending":
            self.color_pending = color
            # Actualizar también el color pendiente para el efecto Aegisub
            # Crear una versión más oscura del color pendiente
            self.aegisub_color_pending = QColor(
                max(0, color.red() - 50),
                max(0, color.green() - 50),
                max(0, color.blue() - 50),
                color.alpha()
            )
        elif color_type == "ball":
            self.color_ball = color
        elif color_type == "next_line":
            self.color_next_line = color
        self.update()

    def _get_lighter_color(self, base_color, lightness_factor=0.5):
        """Genera un color más claro basado en el color base, con caché para optimizar"""
        # Crear una clave única para el caché basada en el color y el factor
        cache_key = f"{base_color.rgba()}_{lightness_factor}"

        # Verificar si el color ya está en caché
        if cache_key in self.aegisub_active_color_cache:
            return self.aegisub_active_color_cache[cache_key]

        # Si no está en caché, calcularlo
        # Obtener los componentes RGB del color base
        r = base_color.red()
        g = base_color.green()
        b = base_color.blue()

        # Calcular los nuevos valores RGB aclarados
        # La fórmula mezcla el color base con blanco según el factor de aclarado
        new_r = int(r + (255 - r) * lightness_factor)
        new_g = int(g + (255 - g) * lightness_factor)
        new_b = int(b + (255 - b) * lightness_factor)

        # Crear el nuevo color
        new_color = QColor(new_r, new_g, new_b, base_color.alpha())

        # Guardar en caché para futuras llamadas
        # Limitar el tamaño del caché para evitar uso excesivo de memoria
        if len(self.aegisub_active_color_cache) > 100:
            # Si el caché es demasiado grande, eliminamos una entrada aleatoria
            self.aegisub_active_color_cache.pop(next(iter(self.aegisub_active_color_cache)))

        self.aegisub_active_color_cache[cache_key] = new_color

        return new_color

    def update_time(self, current_time: float):
        """Actualiza el tiempo actual"""
        # Evitar actualizaciones innecesarias si la posición no ha cambiado significativamente
        # Usamos un umbral extremadamente pequeño para garantizar actualizaciones muy frecuentes
        # Esto reduce el desfase a costa de un poco más de CPU
        if abs(current_time - self.last_position) < 0.002:
            return

        # Guardar el estado anterior
        old_line = self.engine.current_line
        old_note = self.engine.current_note
        old_progress = self.engine.note_progress

        # Actualizar el motor de karaoke con interpolación
        self.last_position = current_time
        self.engine.update(current_time)

        # Comprobar si ha cambiado la línea o la nota
        line_changed = old_line != self.engine.current_line
        note_changed = old_note != self.engine.current_note

        # Siempre actualizar la vista cuando hay una nota activa
        # Esto garantiza una animación fluida y continua
        if self.engine.current_note is not None:
            # Actualizar el estado
            self.last_line = self.engine.current_line
            self.last_note = self.engine.current_note
            self.last_progress = self.engine.note_progress

            # Actualizar la vista
            self.update()
        # Si no hay nota activa, solo actualizar si cambia la línea
        elif line_changed:
            self.last_line = self.engine.current_line
            self.last_note = self.engine.current_note
            self.last_progress = self.engine.note_progress

            # Actualizar la vista
            self.update()

    def paintEvent(self, event):
        """Evento de pintado"""
        # Usar doble buffer para evitar parpadeos
        painter = QPainter(self)

        # Optimizar renderizado para reducir latencia
        # Solo usamos antialiasing para el texto, no para todo
        painter.setRenderHint(QPainter.TextAntialiasing, True)

        # Establecer composición más rápida
        painter.setCompositionMode(QPainter.CompositionMode_Source)

        # Dibujar fondo
        painter.fillRect(self.rect(), Qt.black)

        # Si no hay canción, no dibujar nada más
        if not self.engine.song:
            return

        # Obtener textos
        sung_text, current_syllable, pending_text = self.engine.get_highlighted_text()
        next_line_text = self.engine.get_next_line_text()
        syllable_progress = self.engine.get_syllable_progress()

        # Configurar fuente para la línea actual
        painter.setFont(self.font_current)
        font_metrics = QFontMetrics(self.font_current)

        # Texto completo de la línea actual
        full_text = sung_text + current_syllable + pending_text

        # Calcular posiciones
        line_width = font_metrics.horizontalAdvance(full_text)
        x = (self.width() - line_width) / 2
        y = self.height() / 2

        # Calcular la posición hasta donde se ha cantado (incluyendo la parte de la sílaba actual)
        sung_width = font_metrics.horizontalAdvance(sung_text)
        current_width = font_metrics.horizontalAdvance(current_syllable)

        # Asegurarse de que el progreso sea suave
        progress_width = int(sung_width + (current_width * syllable_progress))

        # Crear un path para el texto completo
        text_path = QPainterPath()
        text_path.addText(int(x), int(y), self.font_current, full_text)

        # Siempre dibujamos el texto completo en color pendiente (blanco)
        # Los efectos overlay se encargarán de borrar y redibujar las partes necesarias
        painter.setPen(Qt.NoPen)
        painter.setBrush(self.color_pending)
        painter.drawPath(text_path)

        # Aplicar el efecto seleccionado
        # Siempre aplicamos el efecto simple de rellenado de izquierda a derecha como base
        if self.current_effect == self.EFFECT_SIMPLE:
            # Solo el efecto simple
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress)
        elif self.current_effect == self.EFFECT_ZOOM:
            # Efecto simple + zoom
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress)
            # Solo aplicamos el zoom a la sílaba actual
            if current_syllable and syllable_progress > 0.0 and syllable_progress < 1.0:
                self._draw_zoom_effect_overlay(painter, x, y, sung_width, current_width, syllable_progress, current_syllable)
        elif self.current_effect == self.EFFECT_PARTICLE:
            # El efecto de partículas incluye el rellenado de izquierda a derecha
            self._draw_particle_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress)
        elif self.current_effect == self.EFFECT_BALL:
            # Efecto simple + bola rebotando
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress)
            self._draw_ball_effect(painter, x, y, sung_width, current_width, syllable_progress)
        elif self.current_effect == self.EFFECT_SHIFT:
            # Efecto simple + desplazamiento
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress)
            # Solo aplicamos el desplazamiento a la sílaba actual
            if current_syllable and syllable_progress > 0.0 and syllable_progress < 1.0:
                self._draw_shift_effect_overlay(painter, x, y, sung_width, current_width, syllable_progress, current_syllable)
        elif self.current_effect == self.EFFECT_WAVE:
            # Efecto de onda con agrandamiento sutil
            # Borramos el fondo para dibujar desde cero
            painter.fillRect(self.rect(), Qt.black)
            # Aplicamos el efecto de onda
            self._draw_wave_effect_overlay(painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text)
        elif self.current_effect == self.EFFECT_PULSE:
            # Efecto simple + pulso
            self._draw_simple_effect(painter, text_path, x, y, sung_width, current_width, syllable_progress)
            # Solo aplicamos el pulso a la sílaba actual
            if current_syllable and syllable_progress > 0.0 and syllable_progress < 1.0:
                self._draw_pulse_effect_overlay(painter, x, y, sung_width, current_width, syllable_progress, current_syllable)
        elif self.current_effect == self.EFFECT_TYPEWRITER:
            # Efecto de máquina de escribir
            # Borramos el fondo para dibujar desde cero
            painter.fillRect(self.rect(), Qt.black)
            # Dibujamos el texto ya cantado
            painter.setPen(Qt.NoPen)
            painter.setBrush(self.color_sung)
            sung_path = QPainterPath()
            sung_path.addText(x, y, self.font_current, sung_text)
            painter.drawPath(sung_path)
            # Aplicamos el efecto de máquina de escribir a la sílaba actual
            self._draw_typewriter_effect_overlay(painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text)
            # Dibujamos el texto pendiente
            painter.setPen(Qt.NoPen)
            painter.setBrush(self.color_pending)
            pending_path = QPainterPath()
            pending_path.addText(x + sung_width + current_width, y, self.font_current, pending_text)
            painter.drawPath(pending_path)
        elif self.current_effect == self.EFFECT_AEGISUB:
            # Efecto estilo Aegisub con transformaciones 3D
            # Borramos el fondo para dibujar desde cero
            painter.fillRect(self.rect(), Qt.black)
            # Aplicamos el efecto Aegisub 3D
            self._draw_aegisub_effect(painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text)


        # Dibujar la siguiente línea
        if next_line_text:
            painter.setFont(self.font_next)
            next_line_width = QFontMetrics(self.font_next).horizontalAdvance(next_line_text)
            x_next = (self.width() - next_line_width) / 2
            y_next = y + font_metrics.height() + 20

            # Crear un path para la siguiente línea
            next_line_path = QPainterPath()
            next_line_path.addText(int(x_next), int(y_next), self.font_next, next_line_text)

            # Dibujar la siguiente línea
            painter.setPen(Qt.NoPen)
            painter.setBrush(self.color_next_line)
            painter.drawPath(next_line_path)

    def _draw_simple_effect(self, painter, text_path, x, y, sung_width, current_width, syllable_progress):
        """Dibuja el efecto simple de resaltado de izquierda a derecha"""
        progress_width = int(sung_width + (current_width * syllable_progress))

        if progress_width > 0:
            # Crear un rectángulo para limitar el dibujo solo a la parte cantada
            painter.save()
            clip_rect = QRect(int(x), 0, progress_width, self.height())
            painter.setClipRect(clip_rect)

            # Dibujar el texto completo en color cantado (verde)
            painter.setBrush(self.color_sung)
            painter.drawPath(text_path)

            painter.restore()

            # Añadir un efecto de transición suave en el borde del texto cantado
            if self.show_transition_effect and syllable_progress > 0.0 and syllable_progress < 1.0:
                transition_width = 10  # Ancho de la transición en píxeles

                # Crear un rectángulo para la transición
                transition_rect = QRect(
                    int(x + progress_width - transition_width/2),
                    0,
                    transition_width,
                    self.height()
                )

                # Crear un degradado para la transición
                gradient = QLinearGradient(
                    transition_rect.left(),
                    0,
                    transition_rect.right(),
                    0
                )
                gradient.setColorAt(0.0, self.color_sung)
                gradient.setColorAt(1.0, self.color_pending)

                # Guardar el estado actual
                painter.save()

                # Establecer el clip para la transición
                painter.setClipRect(transition_rect)

                # Dibujar el texto con el degradado
                painter.setPen(Qt.NoPen)
                painter.setBrush(gradient)
                painter.drawPath(text_path)

                # Restaurar el estado
                painter.restore()

    def _draw_zoom_effect_overlay(self, painter, x, y, sung_width, current_width, syllable_progress, current_syllable):
        """Dibuja el efecto de zoom como complemento al efecto simple"""
        # Calcular el factor de zoom basado en el progreso
        # Máximo zoom al principio, disminuyendo a medida que avanza
        zoom = 1.0 + (self.zoom_factor - 1.0) * (1.0 - syllable_progress)

        # Guardar el estado actual
        painter.save()

        # Crear un path para la sílaba actual
        syllable_path = QPainterPath()

        # Calcular la posición central de la sílaba
        syllable_x = x + sung_width + current_width / 2
        syllable_y = y

        # Borrar el fondo donde irá la sílaba con zoom
        syllable_rect = QRect(int(x + sung_width), 0, int(current_width), self.height())
        painter.fillRect(syllable_rect, Qt.black)

        # Aplicar transformación para el zoom
        painter.translate(syllable_x, syllable_y)
        painter.scale(zoom, zoom)
        painter.translate(-syllable_x, -syllable_y)

        # Dibujar la sílaba actual
        syllable_path.addText(int(x + sung_width), int(y), self.font_current, current_syllable)
        painter.setBrush(self.color_sung)
        painter.drawPath(syllable_path)

        # Restaurar el estado
        painter.restore()

    def _draw_zoom_effect(self, painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text):
        """Dibuja el efecto de zoom en la sílaba actual"""
        # Primero, borrar el fondo donde irá la sílaba actual para evitar que se vea el texto en blanco debajo
        if current_syllable:
            # Crear un rectángulo que cubra el área de la sílaba actual
            syllable_rect = QRect(int(x + sung_width), 0, int(current_width), self.height())
            painter.fillRect(syllable_rect, Qt.black)

        # Dibujar el texto ya cantado
        if sung_width > 0:
            painter.save()
            clip_rect = QRect(int(x), 0, int(sung_width), self.height())
            painter.setClipRect(clip_rect)
            painter.setBrush(self.color_sung)
            painter.drawPath(text_path)
            painter.restore()

        # Dibujar el texto pendiente
        if pending_text:
            painter.save()
            clip_rect = QRect(int(x + sung_width + current_width), 0, int(self.width()), self.height())
            painter.setClipRect(clip_rect)
            painter.setBrush(self.color_pending)
            painter.drawPath(text_path)
            painter.restore()

        # Dibujar la sílaba actual con zoom
        if current_syllable:
            # Calcular el factor de zoom basado en el progreso
            # Máximo zoom al principio, disminuyendo a medida que avanza
            zoom = 1.0 + (self.zoom_factor - 1.0) * (1.0 - syllable_progress)

            # Guardar el estado actual
            painter.save()

            # Crear un path para la sílaba actual
            syllable_path = QPainterPath()

            # Calcular la posición central de la sílaba
            syllable_x = x + sung_width + current_width / 2
            syllable_y = y

            # Aplicar transformación para el zoom
            painter.translate(syllable_x, syllable_y)
            painter.scale(zoom, zoom)
            painter.translate(-syllable_x, -syllable_y)

            # Dibujar la sílaba actual
            syllable_path.addText(int(x + sung_width), int(y), self.font_current, current_syllable)
            painter.setBrush(self.color_sung)
            painter.drawPath(syllable_path)

            # Restaurar el estado
            painter.restore()

    def _draw_particle_effect(self, painter, text_path, x, y, sung_width, current_width, syllable_progress):
        """Dibuja el efecto de partículas que emergen de las letras"""
        progress_width = int(sung_width + (current_width * syllable_progress))

        if progress_width > 0:
            # Parte cantada (izquierda)
            painter.save()
            clip_rect = QRect(int(x), 0, progress_width, self.height())
            painter.setClipRect(clip_rect)
            painter.setBrush(self.color_sung)
            painter.drawPath(text_path)
            painter.restore()

            # Parte pendiente (derecha)
            painter.save()
            clip_rect = QRect(int(x + progress_width), 0, int(self.width() - x - progress_width), self.height())
            painter.setClipRect(clip_rect)
            painter.setBrush(self.color_pending)
            painter.drawPath(text_path)
            painter.restore()

            # Generar partículas en la posición actual del progreso
            particle_x = x + progress_width
            particle_y = y

            # Calcular el número de partículas basado en el progreso
            # Más partículas cuando el progreso es más rápido
            particle_count = int(self.particle_count * (1.0 - syllable_progress) + 2)

            # Dibujar partículas
            for i in range(particle_count):
                # Calcular posición aleatoria alrededor del punto de progreso
                angle = math.radians(random.randint(0, 360))
                distance = random.uniform(0, self.particle_speed * 20 * (1.0 - syllable_progress))

                # Posición de la partícula
                px = particle_x + math.cos(angle) * distance
                py = particle_y + math.sin(angle) * distance

                # Tamaño de la partícula (más pequeña cuanto más lejos)
                size = self.particle_size_max - (distance / (self.particle_speed * 20)) * (self.particle_size_max - self.particle_size_min)
                size = max(self.particle_size_min, size)

                # Opacidad de la partícula (más transparente cuanto más lejos)
                opacity = 1.0 - (distance / (self.particle_speed * 20))
                opacity = max(0.1, opacity)

                # Color de la partícula (siempre usar colores más claros)
                # Cuanto más lejos, más claro el color
                lightness = 0.3 + (distance / (self.particle_speed * 20)) * 0.5  # Valor entre 0.3 y 0.8
                lightness = min(0.8, lightness)  # Limitar a 0.8 para evitar colores demasiado blancos

                # Usar el color más claro para las partículas
                particle_color = self._get_lighter_color(self.color_sung, lightness)

                # Dibujar la partícula
                painter.save()
                painter.setOpacity(opacity)
                painter.setBrush(particle_color)
                painter.setPen(Qt.NoPen)
                painter.drawEllipse(int(px - size/2), int(py - size/2), int(size), int(size))
                painter.restore()

    def _draw_ball_effect(self, painter, x, y, sung_width, current_width, syllable_progress):
        """Dibuja una bola que rebota sobre las sílabas (estilo UltraStar WorldParty)"""
        # Siempre dibujar la bola, incluso si syllable_progress es 0
        # Esto evita el parpadeo cuando cambia la sílaba

        # Calcular la posición horizontal de la bola
        ball_x = x + sung_width + current_width * syllable_progress

        # Aplicar factor de suavidad al movimiento
        # Esto hace que el movimiento sea más lento y natural
        smooth_progress = math.pow(syllable_progress, self.ball_smoothness)

        # Calcular la fase del rebote (ciclo completo)
        bounce_phase = smooth_progress * math.pi * 2

        # Calcular la altura del rebote usando una función sinusoidal
        # Ajustamos la fase para que empiece en el punto más alto
        bounce_height = 50  # Altura máxima del rebote
        bounce_factor = 0.5 * (1 + math.sin(bounce_phase - math.pi/2))

        # Posición vertical de la bola, con mayor separación de las letras
        # Usamos self.ball_offset como la distancia mínima entre la bola y las letras
        ball_y = y - self.ball_offset - bounce_height * bounce_factor

        # Calcular el factor de aplastamiento (squash) basado en la posición vertical
        # La bola se aplasta cuando está cerca del suelo y se estira cuando está en el aire
        # Derivada del seno para determinar la velocidad vertical
        vertical_velocity = math.cos(bounce_phase - math.pi/2)

        # Cuando la velocidad vertical es negativa (cayendo), la bola se estira verticalmente
        # Cuando la velocidad vertical es positiva (subiendo), la bola se estira horizontalmente
        # Cuando la velocidad es cercana a cero (en los extremos), la bola es más circular
        squash_factor = 1.0 - self.ball_squash * vertical_velocity
        stretch_factor = 1.0 / squash_factor  # Conservar el área

        # Guardar el estado actual del pintor
        painter.save()

        # Dibujar la sombra de la bola
        # La sombra se hace más grande y transparente cuando la bola está más alta
        shadow_size = self.ball_size * (1.0 + 0.5 * bounce_factor)
        shadow_alpha = int(self.ball_shadow_alpha * (1.0 - 0.5 * bounce_factor))
        shadow_color = QColor(0, 0, 0, shadow_alpha)

        # Posición de la sombra (siempre en el "suelo", pero más arriba para evitar superposición con las letras)
        # Usamos una fracción mayor de ball_offset para mantener la sombra más alejada de las letras
        shadow_y = y - self.ball_offset/2

        # Dibujar la sombra como una elipse aplanada
        painter.setBrush(shadow_color)
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(
            int(ball_x - shadow_size/2),
            int(shadow_y - shadow_size/8),  # Aplanar la sombra
            int(shadow_size),
            int(shadow_size/4)  # Aplanar la sombra
        )

        # Aplicar transformación para el efecto de squash and stretch
        painter.translate(ball_x, ball_y)
        painter.scale(stretch_factor, squash_factor)

        # Crear un gradiente radial para dar efecto 3D a la bola
        gradient = QRadialGradient(0, 0, self.ball_size)

        # Color base de la bola (verde brillante)
        base_color = self.color_ball

        # Color más oscuro para el borde
        darker_color = QColor(
            max(0, base_color.red() - 50),
            max(0, base_color.green() - 50),
            max(0, base_color.blue() - 50)
        )

        # Color más claro para el centro
        lighter_color = QColor(
            min(255, base_color.red() + 70),
            min(255, base_color.green() + 70),
            min(255, base_color.blue() + 70)
        )

        # Configurar el gradiente
        gradient.setColorAt(0.0, lighter_color)  # Centro más claro
        gradient.setColorAt(0.7, base_color)     # Color base en el medio
        gradient.setColorAt(1.0, darker_color)   # Borde más oscuro

        # Dibujar la bola con el gradiente
        painter.setBrush(gradient)
        painter.setPen(Qt.NoPen)  # Asegurarse de que no haya borde
        painter.drawEllipse(
            int(-self.ball_size/2),
            int(-self.ball_size/2),
            self.ball_size,
            self.ball_size
        )

        # Añadir un brillo (highlight) para dar más efecto 3D
        highlight_size = self.ball_size * self.ball_highlight_size
        highlight_offset = -self.ball_size * 0.2  # Desplazar el brillo hacia arriba-izquierda

        # Crear un gradiente para el brillo
        highlight_gradient = QRadialGradient(
            highlight_offset, highlight_offset, highlight_size
        )
        highlight_gradient.setColorAt(0.0, QColor(255, 255, 255, 180))  # Centro blanco semi-transparente
        highlight_gradient.setColorAt(1.0, QColor(255, 255, 255, 0))    # Borde completamente transparente

        # Dibujar el brillo
        painter.setBrush(highlight_gradient)
        painter.drawEllipse(
            int(highlight_offset - highlight_size/2),
            int(highlight_offset - highlight_size/2),
            int(highlight_size),
            int(highlight_size)
        )

        # Restaurar el estado del pintor
        painter.restore()

    def _draw_wave_effect_overlay(self, painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text):
        """Dibuja el efecto de deformación de letras sin movimiento ondulatorio vertical"""
        # Obtener métricas de la fuente
        font_metrics = QFontMetrics(self.font_current)

        # Calcular el ancho total del texto y el progreso actual
        sung_text_width = font_metrics.horizontalAdvance(sung_text)
        current_syllable_width = font_metrics.horizontalAdvance(current_syllable)
        progress_in_current_syllable = current_syllable_width * syllable_progress

        # Calcular la posición de la frontera de progreso (desde el inicio del texto)
        progress_px = sung_text_width + progress_in_current_syllable

        # Posición inicial
        current_x = x

        # Optimización: Crear paths para texto cantado, sílaba activa y pendiente
        sung_path = QPainterPath()
        active_syllable_path = QPainterPath()  # Nuevo path para la sílaba activa con color fosforescente
        pending_path = QPainterPath()

        # 1. Procesar el texto ya cantado (sung_text) - Color plano
        for idx, char in enumerate(sung_text):
            char_width = font_metrics.horizontalAdvance(char)
            char_center = current_x + char_width / 2

            # Calcular la distancia desde el frente del karaoke
            dist_from_front = abs(char_center - (x + progress_px))

            # Calcular el factor de escala basado en la distancia (exagerado)
            scale_factor = 1.0
            if dist_from_front < self.wave_scale_width * 3:
                # Usar una función exponencial más pronunciada para exagerar el efecto
                scale_factor = 1.0 + (self.wave_max_scale - 1.0) * math.exp(-0.3 * (dist_from_front / self.wave_scale_width) ** 2)

            # Añadir al path cantado con transformación (solo escala, sin desplazamiento vertical)
            painter.save()
            painter.translate(char_center, y)
            painter.scale(scale_factor, scale_factor)

            # Crear un path temporal para este carácter
            char_path = QPainterPath()
            char_path.addText(QPointF(-char_width/2, 0), self.font_current, char)

            # Transformar el path y añadirlo al path cantado
            transformed_path = painter.transform().map(char_path)
            sung_path.addPath(transformed_path)

            painter.restore()

            current_x += char_width

        # 2. Procesar la sílaba actual (current_syllable)
        if current_syllable:
            # Crear un rectángulo que cubra toda la sílaba actual para borrar el fondo
            syllable_rect = QRect(int(x + sung_text_width), 0, int(current_syllable_width), self.height())
            # Borrar el fondo donde irá la sílaba activa para evitar superposiciones
            painter.fillRect(syllable_rect, Qt.black)

            # Dividir la sílaba actual en caracteres
            for idx, char in enumerate(current_syllable):
                char_width = font_metrics.horizontalAdvance(char)
                char_center = current_x + char_width / 2

                # Determinar si este carácter está bajo el progreso
                char_progress = (current_x - x - sung_text_width) / current_syllable_width if current_syllable_width > 0 else 0
                is_karaoke = char_progress <= syllable_progress

                if is_karaoke:
                    # Calcular la distancia desde el frente del karaoke
                    dist_from_front = abs(char_center - (x + progress_px))

                    # Calcular el factor de escala basado en la distancia (exagerado)
                    scale_factor = 1.0
                    if dist_from_front < self.wave_scale_width * 3:
                        # Usar una función exponencial más pronunciada para exagerar el efecto
                        scale_factor = 1.0 + (self.wave_max_scale - 1.0) * math.exp(-0.3 * (dist_from_front / self.wave_scale_width) ** 2)

                    # Añadir al path de sílaba activa con transformación
                    painter.save()
                    painter.translate(char_center, y)
                    painter.scale(scale_factor, scale_factor)

                    # Crear un path temporal para este carácter
                    char_path = QPainterPath()
                    char_path.addText(QPointF(-char_width/2, 0), self.font_current, char)

                    # Transformar el path y añadirlo al path de sílaba activa
                    transformed_path = painter.transform().map(char_path)
                    active_syllable_path.addPath(transformed_path)

                    painter.restore()
                else:
                    # Añadir al path pendiente
                    pending_path.addText(QPointF(current_x, y), self.font_current, char)

                current_x += char_width

        # 3. Procesar el texto pendiente (pending_text)
        if pending_text:
            pending_path.addText(QPointF(current_x, y), self.font_current, pending_text)

        # Dibujar todos los paths de una sola vez (más eficiente)

        # 1. Dibujar el texto cantado (color plano)
        painter.setPen(Qt.NoPen)
        painter.setBrush(self.color_sung)
        painter.drawPath(sung_path)

        # 2. Dibujar la sílaba activa con efecto de luz interior
        if not active_syllable_path.isEmpty():
            # Crear un efecto de luz interior con múltiples capas, pero manteniendo los límites de las letras

            # Primero, crear una máscara con la forma exacta de las letras
            # Esto nos ayudará a recortar cualquier desbordamiento de luz
            painter.save()
            mask_path = QPainterPath(active_syllable_path)  # Copia exacta del path original

            # Configurar el modo de composición para que solo se dibuje dentro de la máscara
            painter.setClipPath(mask_path)

            # Capa 1: Dibujar un gradiente interior para simular luz interior
            # En lugar de agrandar el path, usamos un gradiente dentro del path original
            gradient = QLinearGradient(0, 0, 0, y)
            gradient.setColorAt(0.0, self.inner_light_color)  # Color más brillante arriba
            gradient.setColorAt(1.0, self.outer_light_color)  # Color intermedio abajo

            # Dibujar el gradiente dentro del path original
            painter.setPen(Qt.NoPen)
            painter.setBrush(gradient)
            painter.drawPath(active_syllable_path)

            # Capa 2: Añadir un brillo sutil en los bordes internos
            # Crear un path ligeramente más pequeño para el brillo interior
            inner_glow = QPainterPath(active_syllable_path)  # Copia del path
            transform = painter.transform()
            transform.scale(0.98, 0.98)  # Reducir ligeramente para crear brillo interior
            inner_glow = transform.map(inner_glow)

            # Dibujar el brillo interior
            painter.setPen(Qt.NoPen)
            painter.setBrush(self.inner_light_color)  # Color más brillante para el centro
            painter.drawPath(inner_glow)

            # Restaurar el estado
            painter.restore()

            # Capa 3: Dibujar un contorno sutil para definir mejor los bordes
            painter.save()
            painter.setPen(QPen(self.inner_light_color, 0.5))  # Contorno muy fino
            painter.setBrush(Qt.NoBrush)  # Sin relleno
            painter.drawPath(active_syllable_path)
            painter.restore()

        # 3. Dibujar el texto pendiente
        painter.setPen(Qt.NoPen)
        painter.setBrush(self.color_pending)
        painter.drawPath(pending_path)

    def _draw_shift_effect_overlay(self, painter, x, y, sung_width, current_width, syllable_progress, current_syllable):
        """Dibuja el efecto de desplazamiento como complemento al efecto simple"""
        # Calcular el desplazamiento vertical basado en el progreso
        # Máximo desplazamiento al principio, disminuyendo a medida que avanza
        shift = self.shift_amount * (1.0 - syllable_progress)

        # Guardar el estado actual
        painter.save()

        # Borrar el fondo donde irá la sílaba con desplazamiento
        syllable_rect = QRect(int(x + sung_width), 0, int(current_width), self.height())
        painter.fillRect(syllable_rect, Qt.black)

        # Crear un path para la sílaba actual
        syllable_path = QPainterPath()
        syllable_path.addText(int(x + sung_width), int(y - shift), self.font_current, current_syllable)

        # Dibujar la sílaba actual
        painter.setBrush(self.color_sung)
        painter.drawPath(syllable_path)

        # Restaurar el estado
        painter.restore()

    def _draw_pulse_effect_overlay(self, painter, x, y, sung_width, current_width, syllable_progress, current_syllable):
        """Dibuja el efecto de pulso que hace latir las letras"""
        # Calcular el factor de escala basado en el progreso y una función sinusoidal
        # Esto crea un efecto de latido que va de min_scale a max_scale
        pulse_phase = syllable_progress * self.pulse_speed * math.pi
        scale_factor = self.pulse_min_scale + (self.pulse_max_scale - self.pulse_min_scale) * 0.5 * (1.0 + math.sin(pulse_phase))

        # Guardar el estado actual
        painter.save()

        # Borrar el fondo donde irá la sílaba con pulso
        syllable_rect = QRect(int(x + sung_width), 0, int(current_width), self.height())
        painter.fillRect(syllable_rect, Qt.black)

        # Calcular el centro de la sílaba para aplicar la transformación
        char_center_x = x + sung_width + current_width / 2
        char_center_y = y

        # Aplicar transformación para el pulso (escala desde el centro)
        painter.translate(char_center_x, char_center_y)
        painter.scale(scale_factor, scale_factor)
        painter.translate(-char_center_x, -char_center_y)

        # Crear un path para la sílaba actual
        syllable_path = QPainterPath()
        syllable_path.addText(int(x + sung_width), int(y), self.font_current, current_syllable)

        # Dibujar la sílaba actual con un color que varía con el pulso
        # Más brillante en el pulso máximo, más oscuro en el mínimo
        brightness = int(128 + 127 * ((scale_factor - self.pulse_min_scale) / (self.pulse_max_scale - self.pulse_min_scale)))
        pulse_color = QColor(0, brightness, 0)  # Verde con brillo variable

        painter.setBrush(pulse_color)
        painter.drawPath(syllable_path)

        # Restaurar el estado
        painter.restore()

    def _draw_typewriter_effect_overlay(self, painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text):
        """Dibuja el efecto de máquina de escribir con aparición gradual"""
        # Obtener métricas de la fuente
        font_metrics = QFontMetrics(self.font_current)

        # Calcular el ancho total del texto y el progreso actual
        sung_text_width = font_metrics.horizontalAdvance(sung_text)

        # Borrar el fondo donde irá la sílaba actual
        syllable_rect = QRect(int(x + sung_text_width), 0, int(current_width), self.height())
        painter.fillRect(syllable_rect, Qt.black)

        # Si no hay sílaba actual, no hay nada que hacer
        if not current_syllable:
            return

        # Calcular cuántos caracteres de la sílaba actual deben mostrarse
        char_count = len(current_syllable)
        visible_chars = min(char_count, int(char_count * syllable_progress) + 1)

        # Posición inicial para dibujar los caracteres
        current_x = x + sung_text_width

        # Dibujar cada carácter visible con un efecto de fade-in
        for i in range(visible_chars):
            char = current_syllable[i]
            char_width = font_metrics.horizontalAdvance(char)

            # Calcular la opacidad basada en cuándo apareció el carácter
            # Los caracteres más recientes son más transparentes
            char_progress = (i + 1) / char_count
            char_opacity = min(1.0, (syllable_progress - (char_progress - self.typewriter_char_delay)) / self.typewriter_fade_in)
            char_opacity = max(0.0, char_opacity)  # Asegurarse de que no sea negativo

            # Guardar el estado actual
            painter.save()

            # Configurar la opacidad
            painter.setOpacity(char_opacity)

            # Crear un path para este carácter
            char_path = QPainterPath()
            char_path.addText(int(current_x), int(y), self.font_current, char)

            # Determinar el color basado en si es el carácter activo o ya cantado
            # El carácter activo es el último que ha aparecido completamente
            is_active_char = (i == visible_chars - 1)

            if is_active_char:
                # Para el carácter activo, usar un color más brillante (como en Aegisub)
                active_color = self._get_lighter_color(self.color_sung, 0.4)
                painter.setBrush(active_color)
            else:
                # Para caracteres ya cantados, usar el color normal
                painter.setBrush(self.color_sung)

            # Dibujar el carácter
            painter.setPen(Qt.NoPen)
            painter.drawPath(char_path)

            # Restaurar el estado
            painter.restore()

            # Avanzar a la siguiente posición
            current_x += char_width

        # Dibujar los caracteres pendientes con color opaco
        if visible_chars < char_count:
            # Posición para los caracteres pendientes
            pending_x = current_x

            # Dibujar cada carácter pendiente con color opaco
            for i in range(visible_chars, char_count):
                char = current_syllable[i]
                char_width = font_metrics.horizontalAdvance(char)

                # Crear un path para este carácter
                char_path = QPainterPath()
                char_path.addText(int(pending_x), int(y), self.font_current, char)

                # Dibujar con color pendiente y baja opacidad
                painter.save()
                painter.setOpacity(0.5)  # Opacidad fija para caracteres pendientes
                painter.setPen(Qt.NoPen)
                painter.setBrush(self.color_pending)
                painter.drawPath(char_path)
                painter.restore()

                # Avanzar a la siguiente posición
                pending_x += char_width

            # Efecto de cursor parpadeante al final del texto visible
            # Calcular la posición del cursor
            cursor_x = current_x
            cursor_y = y - font_metrics.ascent()  # Parte superior de la línea de texto
            cursor_height = font_metrics.height()

            # Dibujar el cursor (parpadea basado en el tiempo)
            cursor_blink = (math.sin(syllable_progress * 10) > 0)
            if cursor_blink:
                cursor_rect = QRect(int(cursor_x), int(cursor_y), 2, cursor_height)
                painter.fillRect(cursor_rect, self.color_sung)



    def _draw_aegisub_effect(self, painter, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text):
        """Dibuja el efecto estilo Aegisub con transformaciones 3D inspirado en el archivo .ass"""
        # Obtener métricas de la fuente
        font_metrics = QFontMetrics(self.font_current)

        # Calcular el ancho total del texto y el progreso actual
        sung_text_width = font_metrics.horizontalAdvance(sung_text)
        current_syllable_width = font_metrics.horizontalAdvance(current_syllable)

        # Posición inicial
        current_x = x

        # Aplicar suavizado a las animaciones
        smooth_progress = math.pow(syllable_progress, self.aegisub_animation_smoothness)

        # 1. Dibujar el texto ya cantado con efecto 3D (color azul)
        # Optimización: Procesar caracteres en grupos para reducir llamadas de dibujo
        if sung_text:
            for idx, char in enumerate(sung_text):
                char_width = font_metrics.horizontalAdvance(char)

                # Aplicar transformaciones 3D aleatorias pero estables para cada carácter
                # Usamos el índice del carácter como semilla para el generador de números aleatorios
                # Optimización: Usar una semilla más simple
                random.seed(idx + 1)  # +1 para evitar semilla 0

                # Rotación aleatoria (reducida para suavizar)
                rotation_x = random.uniform(-self.aegisub_rotation_max/6, self.aegisub_rotation_max/6)
                rotation_y = random.uniform(-self.aegisub_rotation_max/6, self.aegisub_rotation_max/6)
                rotation_z = random.uniform(-self.aegisub_rotation_max/6, self.aegisub_rotation_max/6)

                # Escala aleatoria (más cercana a 1.0 para suavizar)
                scale_x = random.uniform(0.97, 1.03)
                scale_y = random.uniform(0.97, 1.03)

                # Usar el color específico para texto ya cantado (azul)
                char_color = self.aegisub_color_sung

                # Dibujar el carácter con transformaciones
                self._draw_aegisub_char(painter, current_x, y, char, char_color,
                                       rotation_x, rotation_y, rotation_z,
                                       scale_x, scale_y, 1.0)  # Opacidad completa

                # Avanzar a la siguiente posición
                current_x += char_width

        # 2. Dibujar la sílaba actual con efectos más intensos
        if current_syllable:
            # Optimización: Pre-calcular el color activo una sola vez
            active_color = self._get_lighter_color(self.aegisub_color_sung, self.aegisub_active_lightness)

            # Crear una fuente en negrita para la sílaba activa (una sola vez)
            bold_font = QFont(self.font_current)
            bold_font.setBold(True)

            # Dividir la sílaba actual en caracteres
            for idx, char in enumerate(current_syllable):
                char_width = font_metrics.horizontalAdvance(char)

                # Determinar si este carácter está bajo el progreso
                char_progress = (idx / len(current_syllable)) if len(current_syllable) > 0 else 0
                is_karaoke = char_progress <= syllable_progress

                if is_karaoke:
                    # Para caracteres ya cantados en la sílaba actual, aplicar efectos más intensos
                    # Optimización: Simplificar la semilla
                    random.seed(idx + 100)

                    # Rotación más suave y animada
                    # Usar funciones sinusoidales suavizadas para las animaciones
                    phase = smooth_progress * math.pi * self.aegisub_transform_speed
                    rotation_factor = 0.5 * (1 + math.sin(phase))  # Valor entre 0 y 1

                    rotation_x = self.aegisub_rotation_max * rotation_factor * random.uniform(0.7, 1.0)
                    rotation_y = self.aegisub_rotation_max * rotation_factor * random.uniform(0.7, 1.0)
                    rotation_z = self.aegisub_rotation_max * rotation_factor * random.uniform(0.7, 1.0)

                    # Escala pulsante más suave
                    scale_phase = smooth_progress * math.pi * 2 * self.aegisub_transform_speed
                    scale_factor = 1.0 + 0.15 * (1 + math.sin(scale_phase)) / 2  # Valor entre 1.0 y 1.15

                    scale_x = scale_factor * random.uniform(self.aegisub_scale_min, self.aegisub_scale_max)
                    scale_y = scale_factor * random.uniform(self.aegisub_scale_min, self.aegisub_scale_max)

                    # Efecto de agrandamiento momentáneo para la sílaba activa (más suave)
                    # El agrandamiento es máximo al inicio y disminuye con el progreso
                    bold_scale = 1.0 + (self.aegisub_bold_scale_factor - 1.0) * (1.0 - smooth_progress)

                    # Dibujar el carácter con transformaciones intensas, negrita y agrandamiento
                    self._draw_aegisub_char(painter, current_x, y, char, active_color,
                                           rotation_x, rotation_y, rotation_z,
                                           scale_x * bold_scale, scale_y * bold_scale, 1.0,
                                           bold_font)
                else:
                    # Para caracteres pendientes en la sílaba actual, usar el color pendiente (azul opaco)
                    # Optimización: Sin transformaciones para caracteres pendientes
                    self._draw_aegisub_char(painter, current_x, y, char, self.aegisub_color_pending,
                                           0, 0, 0, 1.0, 1.0, 1.0, self.font_current)

                # Avanzar a la siguiente posición
                current_x += char_width

        # 3. Dibujar el texto pendiente con el color pendiente (azul opaco)
        # Optimización: Dibujar todo el texto pendiente de una vez en lugar de carácter por carácter
        if pending_text:
            # Dibujar el texto pendiente sin efectos
            painter.setPen(Qt.NoPen)
            painter.setBrush(self.aegisub_color_pending)
            pending_path = QPainterPath()
            pending_path.addText(current_x, y, self.font_current, pending_text)
            painter.drawPath(pending_path)

    def _draw_aegisub_char(self, painter, x, y, char, color, rot_x, rot_y, rot_z, scale_x, scale_y, opacity, font=None):
        """Dibuja un carácter con transformaciones 3D estilo Aegisub (versión optimizada)"""
        # Optimización: Evitar transformaciones si no son necesarias
        if abs(rot_x) < 0.01 and abs(rot_y) < 0.01 and abs(rot_z) < 0.01 and abs(scale_x - 1.0) < 0.01 and abs(scale_y - 1.0) < 0.01:
            # Caso simple: sin transformaciones
            painter.save()

            # Usar la fuente proporcionada o la fuente actual por defecto
            char_font = font if font else self.font_current

            # Establecer la opacidad
            painter.setOpacity(opacity)

            # Dibujar el carácter directamente
            painter.setPen(Qt.NoPen)
            painter.setBrush(color)

            char_path = QPainterPath()
            char_path.addText(x, y, char_font, char)
            painter.drawPath(char_path)

            painter.restore()
            return

        # Caso con transformaciones
        painter.save()

        # Usar la fuente proporcionada o la fuente actual por defecto
        char_font = font if font else self.font_current

        # Crear un path para el carácter
        char_path = QPainterPath()
        char_width = QFontMetrics(char_font).horizontalAdvance(char)

        # Calcular el centro del carácter para las transformaciones
        char_center_x = x + char_width / 2
        char_center_y = y

        # Aplicar transformaciones
        painter.translate(char_center_x, char_center_y)

        # Optimización: Combinar transformaciones cuando sea posible
        # Rotación en Z (rotación en el plano)
        if abs(rot_z) > 0.01:
            painter.rotate(rot_z)

        # Aplicar escala (incluyendo simulación de rotaciones 3D)
        final_scale_x = scale_x
        final_scale_y = scale_y

        # Rotación en X (inclinación vertical) - simulada con escala
        if abs(rot_x) > 0.01:
            scale_y_factor = math.cos(math.radians(rot_x))
            final_scale_y *= scale_y_factor

        # Rotación en Y (inclinación horizontal) - simulada con escala
        if abs(rot_y) > 0.01:
            scale_x_factor = math.cos(math.radians(rot_y))
            final_scale_x *= scale_x_factor

        # Aplicar escala final
        painter.scale(final_scale_x, final_scale_y)

        # Volver al origen para dibujar el carácter
        painter.translate(-char_center_x, -char_center_y)

        # Establecer la opacidad
        painter.setOpacity(opacity)

        # Dibujar el carácter con la fuente especificada
        char_path.addText(x, y, char_font, char)

        # Aplicar efecto de desenfoque si es necesario (optimizado)
        if self.aegisub_blur_radius > 0:
            # Optimización: Reducir el número de pasos de desenfoque
            blur_steps = min(self.aegisub_max_blur_steps, 3)  # Máximo 3 pasos
            blur_opacity = 0.2 / blur_steps

            # Optimización: Usar semilla fija para el desenfoque
            random.seed(42)  # Semilla fija para estabilidad

            for i in range(blur_steps):
                # Calcular desplazamiento con patrón fijo en lugar de aleatorio
                angle = (i / blur_steps) * math.pi * 2
                offset_x = math.cos(angle) * self.aegisub_blur_radius
                offset_y = math.sin(angle) * self.aegisub_blur_radius

                # Guardar estado
                painter.save()

                # Aplicar desplazamiento
                painter.translate(offset_x, offset_y)

                # Dibujar con baja opacidad
                painter.setOpacity(blur_opacity)
                painter.setPen(Qt.NoPen)
                painter.setBrush(color)
                painter.drawPath(char_path)

                # Restaurar estado
                painter.restore()

        # Dibujar el carácter principal
        painter.setPen(Qt.NoPen)
        painter.setBrush(color)
        painter.drawPath(char_path)

        # Restaurar el estado
        painter.restore()

    def _draw_shift_effect(self, painter, text_path, x, y, sung_width, current_width, syllable_progress, sung_text, current_syllable, pending_text):
        """Dibuja el efecto de desplazamiento vertical de la sílaba actual"""
        # Primero, borrar el fondo donde irá la sílaba actual para evitar que se vea el texto en blanco debajo
        if current_syllable:
            # Crear un rectángulo que cubra el área de la sílaba actual
            syllable_rect = QRect(int(x + sung_width), 0, int(current_width), self.height())
            painter.fillRect(syllable_rect, Qt.black)

        # Dibujar el texto ya cantado
        if sung_width > 0:
            painter.save()
            clip_rect = QRect(int(x), 0, int(sung_width), self.height())
            painter.setClipRect(clip_rect)
            painter.setBrush(self.color_sung)
            painter.drawPath(text_path)
            painter.restore()

        # Dibujar el texto pendiente
        if pending_text:
            painter.save()
            clip_rect = QRect(int(x + sung_width + current_width), 0, int(self.width()), self.height())
            painter.setClipRect(clip_rect)
            painter.setBrush(self.color_pending)
            painter.drawPath(text_path)
            painter.restore()

        # Dibujar la sílaba actual con desplazamiento
        if current_syllable:
            # Calcular el desplazamiento vertical basado en el progreso
            # Máximo desplazamiento al principio, disminuyendo a medida que avanza
            shift = self.shift_amount * (1.0 - syllable_progress)

            # Crear un path para la sílaba actual
            syllable_path = QPainterPath()
            syllable_path.addText(int(x + sung_width), int(y - shift), self.font_current, current_syllable)

            # Dibujar la sílaba actual
            painter.setBrush(self.color_sung)
            painter.drawPath(syllable_path)
