�

    ~hh�  �                   �B   � d dl mZmZmZmZ d dlmZmZ  G d� de�      Zy)�    )�QUrl�QTimer�
pyqtSignal�QObject)�QMediaPlayer�
QMediaContentc                   ��   � � e Zd ZdZ ee�      Z ee�      Z ee�      Z	� fd�Z
defd�Zd� Z
d� Zd� Zdefd	�Zd
efd�Zd
efd�Zd
efd
�Zdefd�Zdefd�Zdefd�Zd� Z� xZS )�AudioPlayerz'Clase para reproducir archivos de audioc                 �D  �� t         �| �  �        t        �       | _        | j                  j                  j                  | j                  �       | j                  j                  j                  | j                  �       | j                  j                  j                  | j                  �       t        �       | _        | j                  j                  d�       | j                  j                  j                  | j                  �       d| _        d| _        d| _        d| _        y )N�
   r   T�        )�super�__init__r   �player�positionChanged�connect�_on_position_changed�stateChanged�_on_state_changed�durationChanged�_on_duration_changedr   �update_timer�setInterval�timeout�_emit_position�last_position_update�interpolation_enabled�duration�position)�self�	__class__s    ��vC:\Users\<USER>\Desktop\ultrastar-worldparty-master\ultrastar-worldparty-master\ultrastar_simple\core\audio_player.pyr   zAudioPlayer.__init__   s�   �� �
���� #�n��� 	
���#�#�+�+�D�,E�,E�F���� � �(�(��)?�)?�@����#�#�+�+�D�,E�,E�F� #�H������%�%�b�)����!�!�)�)�$�*=�*=�>� %&��!�%)��"� ��
���
�    �	file_pathc                 �  � | j                  �        d| _        d| _        | j                  j	                  t        t
        j                  |�      �      �       | j                  j                  d�       | j                  j                  d�       y)zCarga un archivo de audior
   N)�stopr   r   r   �setMediar   r   �
fromLocalFiler   �emitr   )r    r$   s     r"   �loadzAudioPlayer.load(   sk   � � 	
�	�	�� ��
���
� 	
�����]�4�+=�+=�i�+H�I�J� 	
���!�!�#�&����!�!�#�&r#   c                 ��   � | j                   j                  d�       d| _        | j                   j                  �        | j                  j                  �        | j                  j                  d�       y)zReproduce el audior   r
   N)r   �setPositionr   �playr   �startr   r)   �r    s    r"   r-   zAudioPlayer.play8   sW   � � 	
������"���
� 	
�����������!� 	
���!�!�#�&r#   c                 �l   � | j                   j                  �        | j                  j                  �        y)u   Pausa la reproducciónN)r   �pauser   r&   r/   s    r"   r1   zAudioPlayer.pauseE   s$   � ������������ r#   c                 �l   � | j                   j                  �        | j                  j                  �        y)u   Detiene la reproducciónN)r   r&   r   r/   s    r"   r&   zAudioPlayer.stopJ   s$   � ������������ r#   r   c                 �R   � | j                   j                  t        |dz  �      �       y)u"   Establece la posición en segundosi�  N)r   r,   �int�r    r   s     r"   �set_positionzAudioPlayer.set_positionO   s   � �������H�t�O� 4�5r#   �returnc                 �   � | j                   S )u'   Obtiene la posición actual en segundos)r   r/   s    r"   �get_positionzAudioPlayer.get_positionS   �   � ��}�}�r#   c                 �   � | j                   S )u    Obtiene la duración en segundos)r   r/   s    r"   �get_durationzAudioPlayer.get_durationW   r:   r#   c                 �X   � | j                   j                  �       t        j                  k(  S )u,   Comprueba si el audio se está reproduciendo)r   �stater   �PlayingStater/   s    r"   �
is_playingzAudioPlayer.is_playing[   s    � ��{�{� � �"�l�&?�&?�?�?r#   c                 �b   � |dz  | _         | j                  j                  | j                   �       y)u)   Manejador para cuando cambia la posición�     @�@N)r   r   r)   r5   s     r"   r   z AudioPlayer._on_position_changed_   �&   � � �6�)��
����!�!�$�-�-�0r#   r>   c                 ��   � | j                   j                  |�       |t        j                  k(  r| j                  j                  �        y| j                  j
                  �        y)z&Manejador para cuando cambia el estadoN)r   r)   r   r?   r   r.   r&   )r    r>   s     r"   r   zAudioPlayer._on_state_changedd   sJ   � ������u�%� �L�-�-�-����#�#�%����"�"�$r#   r   c                 �b   � |dz  | _         | j                  j                  | j                   �       y)u)   Manejador para cuando cambia la duraciónrB   N)r   r   r)   )r    r   s     r"   r   z AudioPlayer._on_duration_changedn   rC   r#   c                 ��  � | j                   j                  �       dz  }| j                  r�| j                  �       rr| j                  j                  �       dz  }| j                  |z   }t
        ||z
  �      dkD  r|| _        n|| _        | j                  j                  | j                  �       y|| j                  k7  r-|| _        | j                  j                  | j                  �       yy)uI   Emite la posición actual (llamado por el timer) con interpolación suaverB   g�������?N)	r   r   r   r@   r   �interval�absr   r)   )r    �actual_position�current_time�interpolated_positions       r"   r   zAudioPlayer._emit_positions   s�   � � �+�+�.�.�0�6�9���%�%�$�/�/�*;��,�,�5�5�7�&�@�L� %)�M�M�L�$@�!� �?�%:�:�;�c�A� /��
� 5��
� 
� � �%�%��d�m�m�4� �$�-�-�/� /��
��$�$�)�)�$�-�-�8� 0r#   )�__name__�
__module__�__qualname__�__doc__r   �floatr   r4   r   r   r   �strr*   r-   r1   r&   r6   r9   r<   �boolr@   r   r   r   r   �
__classcell__)r!   s   @r"   r
   r
      s�   �� �1� !��'�O��c�?�L� ��'�O��0'�c� '� '�!�
!�
6�U� 6��e� ��e� �@�D� @�1�S� 1�
%�s� %�1�S� 1�
9r#   r
   N)	�PyQt5.QtCorer   r   r   r   �PyQt5.QtMultimediar   r   r
   � r#   r"   �<module>rW      s   �� ;� :� :�E9�'� E9r#   
