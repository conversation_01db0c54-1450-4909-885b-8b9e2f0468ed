import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "ZoomEffect"
    
    // Parámetros del efecto zoom (exactos del QPainter original)
    property real zoomFactor: 1.5
    
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0
        
        // Texto cantado (sin efectos)
        Text {
            text: root.sungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }
        
        // Sílaba actual con efecto zoom
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height
            
            // Texto de fondo (color pendiente) - siempre visible como base
            Text {
                id: currentSyllableText
                text: root.currentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }
            
            // Contenedor para la sílaba con zoom (solo cuando hay progreso)
            Item {
                anchors.fill: parent
                visible: root.syllableProgress > 0.0 && root.syllableProgress < 1.0
                
                // Fondo negro para borrar el texto base (como en QPainter)
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }
                
                // Sílaba con zoom (color cantado)
                Text {
                    id: zoomedSyllable
                    anchors.centerIn: parent
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                    
                    // Cálculo exacto del zoom (igual que en QPainter)
                    // Máximo zoom al principio, disminuyendo a medida que avanza
                    scale: {
                        if (root.syllableProgress <= 0 || root.syllableProgress >= 1) {
                            return 1.0
                        }
                        return 1.0 + (zoomFactor - 1.0) * (1.0 - root.syllableProgress)
                    }
                    
                    transformOrigin: Item.Center
                    
                    Behavior on scale {
                        NumberAnimation {
                            duration: 50 // Transición rápida como en el original
                            easing.type: Easing.OutQuad
                        }
                    }
                }
            }
            
            // Sílaba completada (tamaño normal, color cantado)
            Item {
                anchors.fill: parent
                visible: root.syllableProgress >= 1.0
                
                // Fondo negro para borrar el texto base
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }
                
                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                }
            }
        }
        
        // Texto pendiente
        Text {
            text: root.pendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }
    
    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.nextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }
}
