#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import QObject, Signal, Property, Slot
from PySide6.QtGui import QColor

from src.core.karaoke_engine import KaraokeEngine
from src.core.song_loader import Song


class KaraokeBackend(QObject):
    """Karaoke backend for QML interface"""

    # Signals - NOMBRES CORRECTOS PARA QML
    textChanged = Signal()
    effectChanged = Signal()
    colorsChanged = Signal()
    settingsChanged = Signal()
    
    # Señales específicas para propiedades individuales
    currentEffectChanged = Signal()
    sungTextChanged = Signal()
    currentSyllableChanged = Signal()
    pendingTextChanged = Signal()
    syllableProgressChanged = Signal()

    # Effect constants
    EFFECT_SIMPLE = 0
    EFFECT_ZOOM = 1
    EFFECT_PARTICLE = 2
    EFFECT_BALL = 3
    EFFECT_SHIFT = 4
    EFFECT_WAVE = 5
    EFFECT_PULSE = 6
    EFFECT_TYPEWRITER = 7
    EFFECT_AEGISUB = 8
    EFFECT_WORLDWAVE = 9  # ← NUEVO EFECTO AGREGADO

    def __init__(self):
        super().__init__()

        # Karaoke engine
        self.engine = KaraokeEngine()

        # Current effect
        self._current_effect = self.EFFECT_SIMPLE

        # Colors
        self._sung_color = QColor(0, 255, 0)  # Green for sung text
        self._pending_color = QColor(255, 255, 255)  # White for pending text
        self._next_line_color = QColor(128, 128, 128)  # Gray for next line
        self._ball_color = QColor(255, 255, 0)  # Yellow for ball

        # Settings
        self._transition_effect = True
        self._interpolation = True
        self._transition_speed = 6.0
        self._sync_offset = 0.0
        self._smoothing_factor = 0.5
        self._anticipation_time = 0.022

        # Cache para evitar señales innecesarias
        self._last_sung_text = ""
        self._last_current_syllable = ""
        self._last_pending_text = ""
        self._last_syllable_progress = 0.0

    def set_song(self, song: Song):
        """Set current song"""
        self.engine.set_song(song)
        self._emit_text_changes()

    def update_time(self, current_time: float):
        """Update current time"""
        self.engine.update(current_time)
        self._emit_text_changes()

    def _emit_text_changes(self):
        """Emit text change signals only if values actually changed"""
        sung, current, pending = self.engine.get_highlighted_text()
        progress = self.engine.get_syllable_progress()
        
        # Solo emitir señales si los valores cambiaron
        if sung != self._last_sung_text:
            self._last_sung_text = sung
            self.sungTextChanged.emit()
            
        if current != self._last_current_syllable:
            self._last_current_syllable = current
            self.currentSyllableChanged.emit()
            
        if pending != self._last_pending_text:
            self._last_pending_text = pending
            self.pendingTextChanged.emit()
            
        if abs(progress - self._last_syllable_progress) > 0.001:
            self._last_syllable_progress = progress
            self.syllableProgressChanged.emit()
        
        # Emitir señal general
        self.textChanged.emit()

    @Property(str, notify=textChanged)
    def currentLineText(self):
        """Return current line text"""
        return self.engine.get_current_line_text()

    @Property(str, notify=textChanged)
    def nextLineText(self):
        """Return next line text"""
        return self.engine.get_next_line_text()

    @Property(str, notify=sungTextChanged)
    def sungText(self):
        """Return sung text"""
        sung, _, _ = self.engine.get_highlighted_text()
        return sung

    @Property(str, notify=currentSyllableChanged)
    def currentSyllable(self):
        """Return current syllable"""
        _, current, _ = self.engine.get_highlighted_text()
        return current

    @Property(str, notify=pendingTextChanged)
    def pendingText(self):
        """Return pending text"""
        _, _, pending = self.engine.get_highlighted_text()
        return pending

    @Property(float, notify=syllableProgressChanged)
    def syllableProgress(self):
        """Return syllable progress (0.0 - 1.0)"""
        return self.engine.get_syllable_progress()

    @Property(int, notify=currentEffectChanged)
    def currentEffect(self):
        """Return current effect"""
        return self._current_effect

    @currentEffect.setter
    def currentEffect(self, effect):
        """Set current effect"""
        if self._current_effect != effect:
            self._current_effect = effect
            self.currentEffectChanged.emit()
            self.effectChanged.emit()

    @Property(QColor, notify=colorsChanged)
    def sungColor(self):
        """Return sung text color"""
        return self._sung_color

    @sungColor.setter
    def sungColor(self, color):
        """Set sung text color"""
        if self._sung_color != color:
            self._sung_color = color
            self.colorsChanged.emit()

    @Property(QColor, notify=colorsChanged)
    def pendingColor(self):
        """Return pending text color"""
        return self._pending_color

    @pendingColor.setter
    def pendingColor(self, color):
        """Set pending text color"""
        if self._pending_color != color:
            self._pending_color = color
            self.colorsChanged.emit()

    @Property(QColor, notify=colorsChanged)
    def nextLineColor(self):
        """Return next line color"""
        return self._next_line_color

    @nextLineColor.setter
    def nextLineColor(self, color):
        """Set next line color"""
        if self._next_line_color != color:
            self._next_line_color = color
            self.colorsChanged.emit()

    @Property(QColor, notify=colorsChanged)
    def ballColor(self):
        """Return ball color"""
        return self._ball_color

    @ballColor.setter
    def ballColor(self, color):
        """Set ball color"""
        if self._ball_color != color:
            self._ball_color = color
            self.colorsChanged.emit()

    @Property(bool, notify=settingsChanged)
    def transitionEffect(self):
        """Return transition effect setting"""
        return self._transition_effect

    @transitionEffect.setter
    def transitionEffect(self, enabled):
        """Set transition effect"""
        if self._transition_effect != enabled:
            self._transition_effect = enabled
            self.settingsChanged.emit()

    @Property(bool, notify=settingsChanged)
    def interpolation(self):
        """Return interpolation setting"""
        return self._interpolation

    @interpolation.setter
    def interpolation(self, enabled):
        """Set interpolation"""
        if self._interpolation != enabled:
            self._interpolation = enabled
            if enabled:
                self.engine.transition_speed = self._transition_speed
            else:
                self.engine.transition_speed = 100.0
            self.settingsChanged.emit()

    @Property(float, notify=settingsChanged)
    def transitionSpeed(self):
        """Return transition speed"""
        return self._transition_speed

    @transitionSpeed.setter
    def transitionSpeed(self, speed):
        """Set transition speed"""
        if self._transition_speed != speed:
            self._transition_speed = speed
            if self._interpolation:
                self.engine.transition_speed = speed
            self.settingsChanged.emit()

    @Property(float, notify=settingsChanged)
    def syncOffset(self):
        """Return sync offset"""
        return self._sync_offset

    @syncOffset.setter
    def syncOffset(self, offset):
        """Set sync offset"""
        if self._sync_offset != offset:
            self._sync_offset = offset
            self.engine.sync_offset = offset
            self.settingsChanged.emit()

    @Property(float, notify=settingsChanged)
    def smoothingFactor(self):
        """Return smoothing factor"""
        return self._smoothing_factor

    @smoothingFactor.setter
    def smoothingFactor(self, factor):
        """Set smoothing factor"""
        if self._smoothing_factor != factor:
            self._smoothing_factor = factor
            self.settingsChanged.emit()

    @Property(float, notify=settingsChanged)
    def anticipationTime(self):
        """Return anticipation time"""
        return self._anticipation_time

    @anticipationTime.setter
    def anticipationTime(self, time):
        """Set anticipation time"""
        if self._anticipation_time != time:
            self._anticipation_time = time
            self.engine.anticipation_time = time
            self.settingsChanged.emit()

    @Slot(int)
    def setEffect(self, effect):
        """Set karaoke effect"""
        self.currentEffect = effect

    @Slot(result=str)
    def getEffectName(self):
        """Get current effect name"""
        effect_names = [
            "Simple", "Zoom", "Particle", "Ball", "Shift", "Wave", "Pulse", "Typewriter", "Aegisub 3D", "World Wave"  # ← AGREGADO "World Wave"
        ]
        if 0 <= self._current_effect < len(effect_names):
            return effect_names[self._current_effect]
        return "Unknown"

    @Slot(result=bool)
    def hasValidData(self):
        """Check if backend has valid karaoke data"""
        if not self.engine.song:
            return False
        
        sung, current, pending = self.engine.get_highlighted_text()
        return bool(sung or current or pending)

    @Slot(result=str)
    def getBackendStatus(self):
        """Get detailed backend status for debugging"""
        if not self.engine.song:
            return "No song loaded"
        
        sung, current, pending = self.engine.get_highlighted_text()
        progress = self.engine.get_syllable_progress()
        
        status = f"Backend Status:\n"
        status += f"Song: {self.engine.song.title}\n"
        status += f"Current time: {self.engine.current_time:.2f}s\n"
        status += f"Sung text: '{sung}'\n"
        status += f"Current syllable: '{current}'\n"
        status += f"Pending text: '{pending}'\n"
        status += f"Progress: {progress:.3f}\n"
        status += f"Effect: {self.getEffectName()}\n"
        
        return status

    @Slot()
    def forceUpdate(self):
        """Force update of all signals"""
        self._emit_text_changes()
        self.effectChanged.emit()
        self.colorsChanged.emit()
        self.settingsChanged.emit()

    @Slot(result=str)
    def getDebugInfo(self):
        """Get debug information"""
        if not self.engine.song:
            return "No song loaded"

        song = self.engine.song
        gap_seconds = song.get_gap_seconds()

        info = f"Detailed song information:\n\n"
        info += f"Title: {song.title}\n"
        info += f"Artist: {song.artist}\n"
        info += f"BPM: {song.bpm/4:.2f} (internal value: {song.bpm})\n"
        info += f"GAP: {song.gap} ms ({gap_seconds:.2f} s)\n"
        info += f"Genre: {song.genre}\n"
        info += f"Year: {song.year}\n"
        info += f"MP3 file: {song.mp3_file}\n"
        info += f"Full MP3 path: {song.get_full_mp3_path()}\n"
        info += f"Cover: {song.cover}\n"
        info += f"Number of lines: {len(song.lines)}\n"
        info += f"Current karaoke effect: {self.getEffectName()}\n"

        if song.lines:
            first_line = song.lines[0]
            last_line = song.lines[-1]
            info += f"\nFirst line: {first_line.get_text()}\n"
            info += f"First line start time: {first_line.start} beats ({song.get_beat_time(first_line.start):.2f} s)\n"
            info += f"Last line: {last_line.get_text()}\n"
            info += f"Last line end time: {last_line.end} beats ({song.get_beat_time(last_line.end):.2f} s)\n"

            # Show information about first notes
            if first_line.notes:
                first_note = first_line.notes[0]
                info += f"\nFirst note: {first_note.text}\n"
                info += f"First note start time: {first_note.start} beats ({song.get_beat_time(first_note.start):.2f} s)\n"
                info += f"First note duration: {first_note.length} beats ({first_note.length * 60 / song.bpm:.2f} s)\n"

        return info
