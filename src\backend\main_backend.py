#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from PySide6.QtCore import QObject, Signal, Property, Slot, QUrl
from PySide6.QtGui import QColor
from PySide6.QtWidgets import QFileDialog

from src.core.song_loader import Song, SongLoader
from src.core.audio_player import AudioPlayer


class MainBackend(QObject):
    """Main backend for QML interface"""

    # Signals
    songsChanged = Signal()
    currentSongChanged = Signal()
    positionChanged = Signal(float)
    durationChanged = Signal(float)
    playingStateChanged = Signal(bool)

    def __init__(self):
        super().__init__()

        # Components
        self.audio_player = AudioPlayer()
        self.songs = []
        self.current_song = None
        self.karaoke_backend = None

        # Connect audio player signals
        self.audio_player.positionChanged.connect(self._on_position_changed)
        self.audio_player.durationChanged.connect(self._on_duration_changed)
        self.audio_player.stateChanged.connect(self._on_state_changed)

    def set_karaoke_backend(self, karaoke_backend):
        """Set karaoke backend reference"""
        self.karaoke_backend = karaoke_backend

    @Property(list, notify=songsChanged)
    def songList(self):
        """Return list of songs for QML"""
        return [{"artist": song.artist, "title": song.title, "index": i} 
                for i, song in enumerate(self.songs)]

    @Property(str, notify=currentSongChanged)
    def currentSongInfo(self):
        """Return current song information"""
        if not self.current_song:
            return "No song selected"
        
        gap_seconds = self.current_song.get_gap_seconds()
        return (f"{self.current_song.artist} - {self.current_song.title}\n"
                f"BPM: {self.current_song.bpm/4:.2f}, GAP: {self.current_song.gap} ms ({gap_seconds:.2f} s)\n"
                f"Genre: {self.current_song.genre}, Year: {self.current_song.year}\n"
                f"Lines: {len(self.current_song.lines)}")

    @Property(str, notify=currentSongChanged)
    def currentCoverPath(self):
        """Return current song cover path with proper URL formatting"""
        if not self.current_song:
            return ""
        
        cover_path = self.current_song.get_full_cover_path()
        if cover_path and os.path.exists(cover_path):
            # Convert to proper file URL format for QML
            return QUrl.fromLocalFile(cover_path).toString()
        return ""

    @Property(float, notify=positionChanged)
    def position(self):
        """Return current playback position"""
        return self.audio_player.get_position()

    @Property(float, notify=durationChanged)
    def duration(self):
        """Return current song duration"""
        return self.audio_player.get_duration()

    @Property(bool, notify=playingStateChanged)
    def isPlaying(self):
        """Return whether audio is playing"""
        return self.audio_player.is_playing()

    @Slot(str)
    def load_songs(self, folder_path: str):
        """Load songs from folder"""
        print(f"Loading songs from: {folder_path}...")
        self.songs = SongLoader.load_songs_from_directory(folder_path)
        print(f"Songs loaded: {len(self.songs)}/{len(self.songs)}")
        self.songsChanged.emit()

    @Slot()
    def load_songs_folder(self):
        """Open folder dialog to load songs"""
        folder = QFileDialog.getExistingDirectory(None, "Select songs folder")
        if folder:
            self.load_songs(folder)

    @Slot(int)
    def select_song(self, index: int):
        """Select a song by index"""
        if 0 <= index < len(self.songs):
            self.current_song = self.songs[index]
            self._load_current_song()
            self.currentSongChanged.emit()

    @Slot()
    def play(self):
        """Play current song"""
        if self.current_song:
            self.audio_player.play()

    @Slot()
    def pause(self):
        """Pause playback"""
        self.audio_player.pause()

    @Slot()
    def stop(self):
        """Stop playback"""
        self.audio_player.stop()
        self.audio_player.set_position(0)

    @Slot(float)
    def seek(self, position: float):
        """Seek to position (0.0 - 1.0)"""
        if self.audio_player.get_duration() > 0:
            absolute_position = position * self.audio_player.get_duration()
            self.audio_player.set_position(absolute_position)

    @Slot(float, result=str)
    def formatTime(self, seconds):
        """Format time in MM:SS format"""
        if seconds < 0:
            seconds = 0
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def _load_current_song(self):
        """Load current song into audio player"""
        if not self.current_song:
            return

        # Stop current playback
        self.stop()

        # Load audio
        mp3_path = self.current_song.get_full_mp3_path()
        if os.path.exists(mp3_path):
            print(f"Loading audio: {mp3_path}")
            self.audio_player.load(mp3_path)
            
            # Load song into karaoke backend
            if self.karaoke_backend:
                self.karaoke_backend.set_song(self.current_song)
        else:
            print(f"Warning: Could not find audio file: {mp3_path}")

    def _on_position_changed(self, position: float):
        """Handle position changes from audio player"""
        self.positionChanged.emit(position)
        
        # Update karaoke backend
        if self.karaoke_backend:
            self.karaoke_backend.update_time(position)

    def _on_duration_changed(self, duration: float):
        """Handle duration changes from audio player"""
        self.durationChanged.emit(duration)

    def _on_state_changed(self, state: int):
        """Handle state changes from audio player"""
        self.playingStateChanged.emit(self.audio_player.is_playing())
