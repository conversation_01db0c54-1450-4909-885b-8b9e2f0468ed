�

    �th�  �                   �L   � d dl Z d dlZd dlmZ d dlmZ d� Zedk(  r e�        yy)�    N)�QApplication)�
MainWindowc            	      �  � t        t        j                  �      } | j                  d�       t	        �       }|j                  �        t
        t        j                  �      dkD  rSt        j                  j                  t        j                  d   �      r#|j                  t        j                  d   �       n�t        j                  j                  t        j                  j                  t        j                  j                  t        j                  j                  t        �      �      �      d�      }t        j                  j                  |�      r|j                  |�       t        j                  | j!                  �       �       y)u   Función principalzUltraStar Simple�   �songsN)r   �sys�argv�setApplicationNamer   �show�len�os�path�isdir�
load_songs�join�dirname�abspath�__file__�exit�exec_)�app�window�	songs_dirs      �5C:\Users\<USER>\Desktop\ultrastar_basics\src\main.py�mainr      s�   � � �s�x�x�
 �C����-�.� �\�F�
�K�K�M� �3�8�8�}�q��R�W�W�]�]�3�8�8�A�;�7����#�(�(�1�+�&� �G�G�L�L����������������QY�AZ�1[�!\�^e�f�	�
�7�7�=�=��#����i�(� �H�H�S�Y�Y�[��    �__main__)r   r
   �PyQt5.QtWidgetsr   �src.ui.main_windowr   r   �__name__� r   r   �<module>r"      s.   �� � 	� (� *��. �z���F� r   
