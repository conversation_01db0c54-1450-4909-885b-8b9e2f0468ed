�

    �ohN_  �                   ��  � d dl Z d dlZd dlmZmZ d dlmZmZmZm	Z	 e G d� d�      �       Z
e G d� d�      �       Ze G d� d	�      �       Z G d
� d�      Z
edk(  �rd dlZ eej"                  �      d
kD  r�ej"                  d
   Ze
j'                  e�      Zer� edej,                  � dej.                  � ��        edej0                  � dej2                  � ��        edej5                  �       � ��        ed eej6                  �      � ��       ej6                  rDej6                  d    Z edej;                  �       � ��        ed eej<                  �      � ��       yyy ed�       yy)�    N)�	dataclass�field)�List�Dict�Optional�Tuplec                   �D   � e Zd ZU dZeed<   eed<   eed<   eed<   eed<   y)�Noteu.   Representa una nota individual en una canción�type�start�length�pitch�textN)�__name__�
__module__�__qualname__�__doc__�str�__annotations__�int� �    �uC:\Users\<USER>\Desktop\ultrastar-worldparty-master\ultrastar-worldparty-master\ultrastar_simple\core\song_loader.pyr
   r
   
   s   � �8�

�I��J��K��J�

�Ir   r
   c                   �X   � e Zd ZU dZeed<    ee��      Ze	e
   ed<   dZeed<   defd�Z
y	)
�Lineu.   Representa una línea de letra en una canciónr   ��default_factory�notesr   �end�returnc                 �F   � dj                  d� | j                  D �       �      S )u'   Devuelve el texto completo de la línea� c              3   �4   K  � | ]  }|j                   �� � y �w)N)r   )�.0�notes     r   �	<genexpr>z Line.get_text.<locals>.<genexpr>   s   � �� �8�Z�T�t�y�y�Z�s   �)�joinr   ��selfs    r   �get_textz
Line.get_text   s   � ��w�w�8�T�Z�Z�8�8�8r   N)r   r   r   r   r   r   r   �listr   r   r
   r   r   r*   r   r   r   r   r      s2   � �8��J��d�3�E�4��:�3��C��L�9�#� 9r   r   c                   �T  � e Zd ZU dZdZeed<   dZeed<   dZeed<   dZ	e
ed<   dZe
ed<   dZeed	<   dZ
eed
<   dZeed<   dZeed<   dZeed
<   dZeed<   dZeed<    ee��      Zee   ed<   dZeed<   dZeed<   defd�Zdefd�Zde
fd�Zdede
fd�Zde
dee e   e e!   e
f   fd�Z"y)�Songu:   Representa una canción completa con sus metadatos y notasr"   �title�artist�mp3_file�        �bpm�gap�cover�
background�video�genre�edition�languager   �yearr   �lines�	file_path�folder_pathr    c                 ��  � t         j                  j                  | j                  | j                  �      }t         j                  j                  |�      r|S 	 t        j                  | j                  �      D �cg c]#  }|j                  �       j                  d�      r|��% }}|r>|d   | _        t         j                  j                  | j                  | j                  �      S 	 |S c c}w # t        $ r Y |S w xY w)z(Devuelve la ruta completa al archivo MP3�.mp3r   )
�os�pathr'   r=   r0   �exists�listdir�lower�endswith�	Exception)r)   �mp3_path�f�	mp3_filess       r   �get_full_mp3_pathzSong.get_full_mp3_path6   s�   � ��7�7�<�<�� 0� 0�$�-�-�@�� �7�7�>�>�(�#��O�		�$&�J�J�t�/?�/?�$@� 7�$@�q��7�7�9�-�-�f�5� �$@�I� 7� � )�!���
��w�w�|�|�D�$4�$4�d�m�m�D�D� � ���7�� � 	�� ��		�s%   �!C* �8(C%� AC* �%C* �*	C7�6C7c                 �$  � | j                   �s	 g d�}|D ]�  }t        j                  | j                  �      D �cg c]Y  }|j	                  �       j                  |�      r8d|j	                  �       v s$d|j	                  �       v sd|j	                  �       v r|��[ }}|s��|d   | _          n | j                   sa|D ]\  }t        j                  | j                  �      D �cg c]$  }|j	                  �       j                  |�      s�#|��& }}|s�R|d   | _          n | j                   syt        j                  j                  | j                  | j                   �      }t        j                  j                  |�      r|S yc c}w c c}w # t        $ r Y �xw xY w)z0Devuelve la ruta completa a la imagen de portada)z.jpgz.jpegz.pngz.gifz.bmpr4   �front�portadar   r"   )
r4   r@   rC   r=   rD   rE   rF   rA   r'   rB   )r)   �image_extensions�extrH   �potential_covers�images�
cover_paths          r   �get_full_cover_pathzSong.get_full_cover_pathM   sf  � ��z�z�
�#L� �+�C�35�:�:�d�>N�>N�3O� (p�3O�a�)*����);�);�C�)@�'.�!�'�'�)�';�w�!�'�'�)�?S�W`�de�dk�dk�dm�Wm� )*�3O�$� (p� (�%5�a�%8��
��
 ,� �z�z�/��-/�Z�Z��8H�8H�-I�!e�-I��Q�W�W�Y�M_�M_�`c�Md�!�-I��!e�!�)/���D�J�!�	  0� �:�:���W�W�\�\�$�"2�"2�D�J�J�?�
� �7�7�>�>�*�%��� ��7(p�� "f�� � 
��
�sA   �*F �AE9�F �?F �$E>� E>�F �	
F �9
F �	F�Fc                 �@   � | j                   dk  ry| j                   dz  S )zDevuelve el GAP en segundosr   r1   g     @�@)r3   r(   s    r   �get_gap_secondszSong.get_gap_secondsq   s#   � � �8�8�a�<�� �x�x�&� � r   �beatc                 �h   � | j                   dk  ry|dz  | j                   z  | j                  �       z   S )z&Convierte un beat en tiempo (segundos)r   �<   )r2   rU   )r)   rV   s     r   �
get_beat_timezSong.get_beat_timez   s1   � ��8�8�q�=���r�	�D�H�H�$��(<�(<�(>�>�>r   �current_timec                 �p  � | j                  �       }||k  ry||z
  | j                  z  dz  }d}| j                  D ]5  }||j                  k\  r||j                  k  r|} n||j                  k  s�5 n |syd}t        |j                  �      D ]�  \  }}|j                  |j                  z   }	||j                  k\  s�/||	k  s�5|}||j                  z
  |j                  z  }
d}|
dk  r|
dz  }|
d|d|z
  z  z
  z  }
n
|
dkD  r|
}
n|
}
t        dt        d|
�      �      }|||fc S  |j                  �r|j                  d	   }|j                  d
   }||j                  k  r|j                  |z
  }|dk  r||dfS |ddfS ||j                  |j                  z   k\  r||dfS t        t        |j                  �      dz
  �      D ]t  }|j                  |   }|j                  |dz      }|j                  |j                  z   }	||	k\  s�C||j                  k  s�S||	z
  |j                  |z
  k  r||dfc S ||dfc S  |ddfS )u~   
        Obtiene la línea y nota actuales basadas en el tiempo actual
        Devuelve (línea, nota, progreso_nota)
        )NNr1   rX   Ng      �?g�������?g      �?g�������?r1   r   ������   )rU   r2   r;   r   r   �	enumerater   r
   �max�min�range�len)r)   rZ   �gap_seconds�current_beat�current_line�line�current_note�ir%   �note_end�raw_progress�smoothing_factor�t�smoothed_progress�progress�
first_note�	last_note�
time_to_first�	next_notes                      r   �get_current_line_and_notezSong.get_current_line_and_note�   s�  � � �*�*�,���+�%�"� %�{�2�d�h�h�>��C�� ���J�J�D��t�z�z�)�l�d�h�h�.F�#�����
�
�*� � � �"� �� ��!3�!3�4�G�A�t��z�z�D�K�K�/�H��t�z�z�)�l�X�.E�#�� !-�t�z�z� 9�T�[�[�H�� $'� �  �#�%� %�s�*�A�(4��>N�RU�XY�RY�>Z�8Z�([�%�!�C�'� )5�%� )5�%� �s�C��->�$?�@��#�\�8�;�;�I 5�P ���%�+�+�A�.�J�$�*�*�2�.�I��j�.�.�.� !+� 0� 0�<� ?�
� �3�&�'��S�8�8�'��s�2�2�����9�3C�3C�!C�C� $�Y��3�3� �s�<�#5�#5�6��:�;�A�'�-�-�a�0�D� ,� 2� 2�1�q�5� 9�I�#�z�z�D�K�K�7�H�#�x�/�L�9�?�?�4R� (�(�2�Y�_�_�|�5S�S�#/��s�#:�:� $0��C�#?�?� <� �T�3�&�&r   N)#r   r   r   r   r.   r   r   r/   r0   r2   �floatr3   r4   r5   r6   r7   r8   r9   r:   r   r   r+   r;   r   r   r<   r=   rJ   rS   rU   rY   r   r   r
   rs   r   r   r   r-   r-       s  � �D��E�3�O��F�C���H�c���C����C����E�3�O��J����E�3�O��E�3�O��G�S���H�c���D�#�M��d�3�E�4��:�3� �I�s���K����3� �."�S� "�H!�� !�?�#� ?�%� ?�f'�e� f'��h�t�n�V^�_c�Vd�fk�Fk�@l� f'r   r-   c                   �P   � e Zd ZdZededee   fd��       Zedede	e   fd��       Z
y)�
SongLoaderz;Clase para cargar y parsear archivos de canciones UltraStarr<   r    c           
      ��  � t         j                  j                  | �      st        d| � d��       yg d�}d}|D ]+  }	 t	        | d|��      5 }|j                  �       }ddd�        n |�Qt        d
| � d��       	 t	        | d�      5 }|j                  �       j                  d
d��      }ddd�       t        d�       t        �       }| |_
        t         j                  j                  | �      |_        |j                  �       }d}|t        |�      k  �r�||   r||   j                  d�      �r�||   j!                  �       }	|	j                  d�      �rt#        j$                  d|	�      }
|
�rf|
j'                  d�      j)                  �       j!                  �       }|
j'                  d�      j!                  �       }|dk(  r	||_        �n|dk(  r||_        n�|dk(  r||_        n�|dk(  r;	 t#        j0                  dd|j!                  �       �      }
	 t3        |
�      dz  |_        n�|d"k(  rI	 t#        j0                  d#d|j!                  �       �      }
	 t3        |
�      }|dkD  r
|d$k  r|d%z  }||_        nd|d'k(  r||_        nW|d(k(  r||_         nJ|d)k(  r||_!        n=|d*k(  r||_"        n0|d+k(  r||_#        n#|d,k(  r||_$        n|d-k(  r	 tK        |�      |_&        |dz
  }|t        |�      k  r||   s���||   j                  d�      r���g }t         j                  jO                  | �      }t         j                  jO                  t         j                  j                  | �      �      }|jQ                  d!d�      }|j*                  s�t        |�      dkD  r|d   |_        n�t         j                  jS                  |�      d   jQ                  d!d�      }t        |�      dkD  r|d   |_        n8t         j                  jS                  |�      d   |_        |jU                  d�       |j,                  s|t        |�      dkD  r|d   |_        nct         j                  jS                  |�      d   jQ                  d!d�      }t        |�      dkD  r|d   |_        nd/|_        |jU                  d�       |j.                  �st        jV                  t         j                  j                  | �      �      D �cg c]#  }|jY                  �       j[                  d0�      r|��% }}|r|d   |_        n�t         j                  jS                  |�      d   � d0�}t         j                  j                  t         j                  j]                  t         j                  j                  | �      |�      �      r||_        n|jU                  d�       |j4                  dk  r�	 t	        | dd
d�1�      5 }|j                  �       }t#        j^                  d2|t"        j`                  �      }|r6|j'                  d�      j9                  dd�      }	 t3        |�      dz  |_        nd3|_        |jU                  d�       ddd�       |r+t        d4| � d	d5j]                  |�      � ��       t        d6�       |j.                  st        d7| � ��       y|j<                  dk  r�	 t	        | dd
d�1�      5 }|j                  �       }t#        j^                  d8|t"        j`                  �      }|rC|j'                  d�      j9                  dd�      }	 t3        |�      }|dkD  r
|d$k  r|d%z  }||_        ddd�       |j<                  dk  rd|_        d}|t        |�      k  �r�||   j!                  �       }	|dz
  }|	s�*|	j                  d9�      r�|	jQ                  �       }t        |�      dk\  rb	 |r,|jb                  r tK        |d   �      |jb                  d:   _2        tg        tK        |d   �      �;�      }|jb                  jU                  |�       ��|	d   d=v r�|	jQ                  d>d?�      }t        |�      dk\  r�	 |d   }tK        |d   �      }tK        |d   �      }tK        |d?   jQ                  d>�      d   �      }d>j]                  |d?   jQ                  d>�      dd �      }|s'tg        |�;�      }|jb                  jU                  |�       ti        |||||�@�      } |jj                  jU                  | �       tm        |jd                  ||z   �      |_2        ���|t        |�      k  r���|jb                  st        dB| � ��       y|S # 1 sw Y   ���xY w# t        $ r Y �	�(t        $ r}t        d| � d|� d	|� ��       Y d}~�	�Jd}~ww xY w# 1 sw Y   ���xY w# t        $ r}t        d| � d	|� ��       Y d}~yd}~ww xY w# t6        $ r) |
j9                  dd�      }t3        |�      dz  |_        Y ���w xY w# t        $ r%}t        d |� d!t;        |�      � ��       Y d}~��Od}~ww xY w# t6        $ r t3        |
j9                  dd�      �      }Y ���w xY w# t        $ r%}t        d&|� d!t;        |�      � ��       Y d}~���d}~ww xY w# t6        $ r t        d.|� ��       Y ���w xY wc c}w # t6        $ r d3|_        |jU                  d�       Y ��vw xY w# 1 sw Y   ��|xY w# t        $ r d3|_        |jU                  d�       Y ���w xY w# t6        $ r Y ���w xY w# 1 sw Y   ���xY w# t        $ r Y ���w xY w# t6        $ r t        d<|	� ��       Y ��1w xY w# t6        $ r t        dA|	� ��       Y ��Mw xY w)Cu(   Carga una canción desde un archivo .txtzError: El archivo �
 no existeN)�utf-8zlatin-1�cp1252z
iso-8859-1�r)�encodingzError al leer el archivo u    con codificación z: z"Error: No se pudo leer el archivo u#    con ninguna codificación conocida�rbry   �ignore)�errorsu4   Archivo leído en modo binario con errores ignoradoszError fatal al leer el archivo r   �#z#\s*([^:]+)\s*:(.*)r]   �   �TITLE�ARTIST�MP3�BPMz[^\d.,]r"   �   �,�.zError al parsear BPM: � - �GAPz	[^\d.,\-]�d   i�  zError al parsear GAP: �COVER�
BACKGROUND�VIDEO�GENRE�EDITION�LANGUAGE�YEARzError al parsear YEAR: �Desconocidor?   )r|   r   z#\s*BPM\s*:\s*(\d+[.,]?\d*)g      n@z!Advertencia: Campos faltantes en z, z*Usando valores derivados o predeterminadosz0Error: No se pudo encontrar un archivo MP3 para z#\s*GAP\s*:\s*(\d+[.,]?\d*)�-r\   )r   u    Error al parsear fin de línea: )�:�*�F�R�G� �   )r   r   r
   r   r   zError al parsear nota: u$   Error: No se encontraron líneas en )7r@   rA   rB   �print�open�read�UnicodeDecodeErrorrF   �decoder-   r<   �dirnamer=   �
splitlinesrb   �
startswith�strip�re�match�group�upperr.   r/   r0   �subrt   r2   �
ValueError�replacer   r3   r4   r5   r6   r7   r8   r9   r   r:   �basename�split�splitext�appendrC   rD   rE   r'   �search�
IGNORECASEr;   r   r   r
   r   r_   )!r<   �	encodings�contentr|   rH   �e�songr;   rh   rf   �header_match�header_name�header_value�
cleaned_value�	bpm_value�	gap_value�missing_fields�	file_name�folder_name�folder_parts�
file_partsrI   �
potential_mp3�	bpm_match�	gap_matchre   �parts�	note_typer   r
   r   r   r%   s!                                    r   �	load_songzSongLoader.load_song�   s�
  � � �w�w�~�~�i�(��&�y�k��<�=�� A�	��� "�H�
a��)�S�8�<���f�f�h�G� =� � "� �?��6�y�k�Ad�e�f�
��)�T�*�a��f�f�h�o�o�g�h�o�G�G� +��L�N� �v��"����7�7�?�?�9�5��� �"�"�$�� 
���#�e�*�n�e�A�h�%��(�2E�2E�c�2J���8�>�>�#�D����s�#�  "�x�x�(>��E���".�"4�"4�Q�"7�"=�"=�"?�"E�"E�"G�K�#/�#5�#5�a�#8�#>�#>�#@�L�"�g�-�%1��
�$��0�&2���$��-�(4��
�$��-�V�,.�F�F�:�r�<�CU�CU�CW�,X�M�@�+0��+?�!�+C���� %��-�V�,.�F�F�<��\�EW�EW�EY�,Z�M�S�,1�-�,@�	�  )�1�}��S�� )�T� 1�	�'0�D�H� %��/�%1��
�$��4�*6���$��/�%1��
�$��/�%1��
�$�	�1�'3���$�
�2�(4��
�$��.�L�(+�L�(9�D�I� 
��F�A�M �#�e�*�n�e�A�h�%��(�2E�2E�c�2J�R �� �G�G�$�$�Y�/�	��g�g�&�&�r�w�w���y�'A�B�� #�(�(���2���z�z��<� �1�$�)�!�_��
�  �W�W�-�-�i�8��;�A�A�%��K�
��z�?�Q�&�!+�A��D�J�!#���!1�!1�)�!<�Q�!?�D�J�"�)�)�'�2��{�{��<� �1�$�*�1�o���  �W�W�-�-�i�8��;�A�A�%��K�
��z�?�Q�&�",�Q�-�D�K�"/�D�K�"�)�)�(�3��}�}�$&�J�J�r�w�w���y�/I�$J� 7�$J�q��7�7�9�-�-�f�5� �$J�I� 7� � )�!���
�
 $&�7�7�#3�#3�I�#>�q�#A�"B�$� G�
��7�7�>�>�"�'�'�,�,�r�w�w���y�/I�=�"Y�Z�$1�D�M�"�)�)�%�0��8�8�q�=�
-��)�S�7�8�L�PQ��f�f�h�G� "�	�	�*H�'�SU�S`�S`� a�I� �$-�O�O�A�$6�$>�$>�s�C�$H�	�9�',�Y�'7�!�';�D�H�
 $)���&�-�-�e�4� M�$ ��5�i�[��4�9�9�^�C\�B]�^�_��>�@� �}�}��D�Y�K�P�Q�� �8�8�q�=�
��)�S�7�8�L�PQ��f�f�h�G� "�	�	�*H�'�SU�S`�S`� a�I� �$-�O�O�A�$6�$>�$>�s�C�$H�	�!�(-�i�(8�I�(�1�}��S�� )�T� 1�	�'0�D�H� M�$ �x�x�1�}� ��� ���#�e�*�n���8�>�>�#�D�
��F�A��� ���s�#��
�
����u�:��?�	I�'�D�J�J�14�U�1�X��D�J�J�r�N�.� (,�#�e�A�h�-�'@���
�
�)�)�,�7� � �A�w�3�3��
�
�3��*���u�:��?�@�$)�!�H�	� #�E�!�H�
��!$�U�1�X��� #�E�!�H�N�N�3�$7��$:� ;��"�x�x��a����s�(;�A�B�(?�@��  ,�+/�e�+<�L� �J�J�-�-�l�;�  $��%��V[�bf�g��$�*�*�1�1�$�7� ,/�|�/?�/?����+P��(� �_ �#�e�*�n�d �z�z��8���D�E�� ��i =�<�� &� 
��� 
a��1�)��<O�PX�z�Y[�\]�[^�_�`�`��
a�� +�*�� � 
��7�	�{�"�Q�C�H�I���
��L $.� @�,9�,A�,A�#�s�,K�	�+0��+;�a�+?���@��  )� V�!�$:�<�.��C�PQ�F�8�"T�U�U��V�� $.� S�,1�-�2G�2G��S�2Q�,R�	�S��  )� V�!�$:�<�.��C�PQ�F�8�"T�U�U��V��"  *� L�!�$;�L�>�"J�K�L��V7��2  *� 9�',�D�H�*�1�1�%�8�9�� M�L�� � 
-� ����%�%�e�,�
-��<  *� !� �!�� M�L�� � 
��
��> &� I�� @���G�H�I��4 &� @�� 7��v�>�?�@�s�  �e�
e�e�8f �"f
�&f �!%g3 �f> �!%i �h$ �i �=j  �(j�<k �Ak�%j#�8k�k �1l � Al�!l �;l �5A!l- �?Cm �e	�e�	f� f�(f�f�
f�f �	f;� f6�6f;�>.g0�,g3 �/g0�0g3 �3	h!�<h�h!�$$i�i �i�i �	i=�i8�8i=� j�j�#!k�k�k�k�k�k �!k=�<k=� 	l
�	l�l
�
l�l�l �	l*�)l*�-m�m�m&�%m&�	directoryc                 �  � g }d}d}t         j                  j                  | �      st        d| � d��       |S t        d| � d��       t        j                  | �      D ]�  \  }}}|D ]u  }|j                  �       j
                  d�      s�#|dz
  }t         j                  j                  ||�      }t        j                  |�      }	|	r|j                  |	�       �q|dz
  }�w �� t        dt        |�      � d	|� ��       |dkD  rt        d
|� ��       |S )z*Carga todas las canciones de un directorior   zError: El directorio rx   zCargando canciones desde: z...z.txtr]   zCanciones cargadas: �/zCanciones con errores: )r@   rA   rB   r�   �walkrD   rE   r'   rv   r�   r�   rb   )
r�   �songs�error_count�total_files�root�_�files�filer<   r�   s
             r   �load_songs_from_directoryz$SongLoader.load_songs_from_directory  s  � � �������w�w�~�~�i�(��)�)��J�?�@��L� 	�*�9�+�S�9�:� !�g�g�i�0�N�D�!�U����:�:�<�(�(��0��1�$�K� "�����T�4� 8�I�%�/�/�	�:�D�����T�*�#�q�(�� � 1� 	�$�S��Z�L��+��?�@���?��+�K�=�9�:��r   N)r   r   r   r   �staticmethodr   r   r-   r�   r   r�   r   r   r   rv   rv   �   sT   � �E��a�S� a�X�d�^� a� �a�F	 ��S� �T�$�Z� � �r   rv   �__main__r]   u
   Canción: r�   zBPM: z, GAP: z
Archivo MP3: u	   Líneas: u   Primera línea: u   Notas en la primera línea: z0Uso: python song_loader.py <ruta_al_archivo_txt>)r@   r�   �dataclassesr   r   �typingr   r   r   r   r
   r   r-   rv   r   �sysrb   �argvr<   r�   r�   r�   r/   r.   r2   r3   rJ   r;   rf   r*   r   r   r   r   �<module>r�      sz  �� 
� 	� (� .� .� �� � �� �9� 9� �9� �E'� E'� �E'�PF� F�R
 �z���
�3�8�8�}�q���H�H�Q�K�	��#�#�I�.����J�t�{�{�m�3�t�z�z�l�;�<��E�$�(�(��7�4�8�8�*�5�6��M�$�"8�"8�":�!;�<�=��I�c�$�*�*�o�.�/�0� �z�z��z�z�!�}���(�����(9�:�;��4�S����_�4E�F�G� � � 	�@�A�' r   
