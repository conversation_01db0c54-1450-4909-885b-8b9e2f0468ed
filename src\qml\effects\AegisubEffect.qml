import QtQuick 2.15
import QtGraphicalEffects 1.15
import "../components"

EffectBase {
    id: root
    effectName: "AegisubEffect"
    
    // Parámetros exactos del QPainter original (adaptables al tamaño de fuente)
    property real aegisubRotationMax: 15.0
    property real aegisubScaleMin: 0.9
    property real aegisubScaleMax: 1.2
    property real aegisubActiveLightness: 0.4
    property real aegisubAnimationSmoothness: 0.8
    property real aegisubTransformSpeed: 1.5
    property real aegisubBoldScaleFactor: 1.15
    property real aegisubBlurRadius: 1.0
    property int aegisubMaxBlurSteps: 3
    
    // Colores específicos del efecto Aegisub (como en el original)
    readonly property color aegisubColorSung: root.sungColor
    readonly property color aegisubColorPending: Qt.darker(root.pendingColor, 1.2)
    
    // Timer independiente para animaciones constantes (como en el original)
    property real constantAnimationTime: 0.0
    property real smoothProgress: Math.pow(root.syllableProgress, aegisubAnimationSmoothness)
    
    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS para animaciones fluidas
        onTriggered: {
            root.constantAnimationTime += 0.016
            if (root.constantAnimationTime > Math.PI * 8) {
                root.constantAnimationTime -= Math.PI * 8
            }
        }
    }
    
    // Propiedades seguras con validación
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0
    
    // TextMetrics para posicionamiento preciso
    TextMetrics {
        id: sungTextMetrics
        font: root.textFont
        text: root.safeSungText
    }
    
    TextMetrics {
        id: currentSyllableMetrics
        font: root.textFont
        text: root.safeCurrentSyllable
    }
    
    // Función para generar color más claro (igual que en QPainter)
    function getLighterColor(baseColor, lightnessFactor) {
        var r = baseColor.r
        var g = baseColor.g
        var b = baseColor.b
        
        var newR = r + (1.0 - r) * lightnessFactor
        var newG = g + (1.0 - g) * lightnessFactor
        var newB = b + (1.0 - b) * lightnessFactor
        
        return Qt.rgba(newR, newG, newB, baseColor.a)
    }
    
    // Función para generar valores pseudo-aleatorios estables (como en QPainter)
    function pseudoRandom(seed, min, max) {
        // Implementación simple de generador pseudo-aleatorio estable
        var x = Math.sin(seed * 12.9898) * 43758.5453
        var frac = x - Math.floor(x)
        return min + (max - min) * Math.abs(frac)
    }
    
    Row {
        id: mainTextRow
        anchors.centerIn: parent
        spacing: 0
        
        // 1. TEXTO CANTADO con transformaciones 3D aleatorias pero estables
        Row {
            spacing: 0
            
            Repeater {
                model: root.safeSungText.length
                
                Item {
                    property int charIndex: index
                    property string char: (charIndex < root.safeSungText.length) ? root.safeSungText.charAt(charIndex) : ""
                    
                    // Valores aleatorios estables basados en índice (igual que QPainter)
                    property real baseRotX: root.pseudoRandom(charIndex + 1, -root.aegisubRotationMax/6, root.aegisubRotationMax/6)
                    property real baseRotY: root.pseudoRandom(charIndex + 2, -root.aegisubRotationMax/6, root.aegisubRotationMax/6)
                    property real baseRotZ: root.pseudoRandom(charIndex + 3, -root.aegisubRotationMax/6, root.aegisubRotationMax/6)
                    property real baseScaleX: root.pseudoRandom(charIndex + 4, 0.97, 1.03)
                    property real baseScaleY: root.pseudoRandom(charIndex + 5, 0.97, 1.03)
                    
                    width: charText.width
                    height: charText.height
                    
                    Text {
                        id: charText
                        text: parent.char
                        font: root.textFont
                        color: root.aegisubColorSung
                        antialiasing: true
                        
                        transform: [
                            Rotation {
                                angle: parent.baseRotX + root.aegisubRotationMax * 0.1 * Math.sin(root.constantAnimationTime + parent.charIndex * 0.3)
                                axis { x: 1; y: 0; z: 0 }
                                origin.x: charText.width / 2
                                origin.y: charText.height / 2
                            },
                            Rotation {
                                angle: parent.baseRotY + root.aegisubRotationMax * 0.1 * Math.cos(root.constantAnimationTime + parent.charIndex * 0.3)
                                axis { x: 0; y: 1; z: 0 }
                                origin.x: charText.width / 2
                                origin.y: charText.height / 2
                            },
                            Rotation {
                                angle: parent.baseRotZ + root.aegisubRotationMax * 0.05 * Math.sin(root.constantAnimationTime + parent.charIndex * 0.7)
                                axis { x: 0; y: 0; z: 1 }
                                origin.x: charText.width / 2
                                origin.y: charText.height / 2
                            },
                            Scale {
                                xScale: parent.baseScaleX + 0.02 * Math.sin(root.constantAnimationTime + parent.charIndex * 0.4)
                                yScale: parent.baseScaleY + 0.02 * Math.cos(root.constantAnimationTime + parent.charIndex * 0.4)
                                origin.x: charText.width / 2
                                origin.y: charText.height / 2
                            }
                        ]
                        
                        // Efecto de sombra/desenfoque (simulando el blur del QPainter)
                        layer.enabled: true
                        layer.effect: DropShadow {
                            horizontalOffset: 2
                            verticalOffset: 2
                            radius: root.aegisubBlurRadius * 4
                            samples: 9
                            color: "#60000000"
                        }
                    }
                }
            }
        }
        
        // 2. SÍLABA ACTUAL con efectos intensificados (lógica exacta del QPainter)
        Row {
            spacing: 0
            
            Repeater {
                model: root.safeCurrentSyllable.length
                
                Item {
                    property int charIndex: index
                    property string char: (charIndex < root.safeCurrentSyllable.length) ? root.safeCurrentSyllable.charAt(charIndex) : ""
                    property real charProgress: root.safeCurrentSyllable.length > 0 ? charIndex / root.safeCurrentSyllable.length : 0
                    property bool isKaraoke: charProgress <= root.safeSyllableProgress
                    
                    width: charText.width
                    height: charText.height
                    
                    Text {
                        id: charText
                        text: parent.char
                        font.family: root.textFont.family
                        font.pixelSize: root.textFont.pixelSize
                        font.bold: parent.isKaraoke ? true : root.textFont.bold
                        antialiasing: true
                        
                        // Color exacto del QPainter original
                        color: parent.isKaraoke ? root.getLighterColor(root.aegisubColorSung, root.aegisubActiveLightness) : root.aegisubColorPending
                        
                        transform: parent.isKaraoke ? activeTransforms : pendingTransforms
                        
                        // Transformaciones para caracteres activos (lógica exacta del QPainter)
                        property list<Transform> activeTransforms: [
                            Rotation {
                                // Rotación más intensa y animada (igual que QPainter)
                                property real rotationFactor: 0.5 * (1 + Math.sin(root.smoothProgress * Math.PI * root.aegisubTransformSpeed))
                                angle: root.aegisubRotationMax * rotationFactor * root.pseudoRandom(parent.charIndex + 100, 0.7, 1.0)
                                axis { x: 1; y: 0; z: 0 }
                                origin.x: charText.width / 2
                                origin.y: charText.height / 2
                            },
                            Rotation {
                                property real rotationFactor: 0.5 * (1 + Math.sin(root.smoothProgress * Math.PI * root.aegisubTransformSpeed))
                                angle: root.aegisubRotationMax * rotationFactor * root.pseudoRandom(parent.charIndex + 101, 0.7, 1.0)
                                axis { x: 0; y: 1; z: 0 }
                                origin.x: charText.width / 2
                                origin.y: charText.height / 2
                            },
                            Rotation {
                                property real rotationFactor: 0.5 * (1 + Math.sin(root.smoothProgress * Math.PI * root.aegisubTransformSpeed))
                                angle: root.aegisubRotationMax * rotationFactor * root.pseudoRandom(parent.charIndex + 102, 0.7, 1.0)
                                axis { x: 0; y: 0; z: 1 }
                                origin.x: charText.width / 2
                                origin.y: charText.height / 2
                            },
                            Scale {
                                // Escala pulsante más suave (igual que QPainter)
                                property real scalePhase: root.smoothProgress * Math.PI * 2 * root.aegisubTransformSpeed
                                property real scaleFactor: 1.0 + 0.15 * (1 + Math.sin(scalePhase)) / 2
                                property real boldScale: 1.0 + (root.aegisubBoldScaleFactor - 1.0) * (1.0 - root.smoothProgress)
                                
                                xScale: scaleFactor * root.pseudoRandom(parent.charIndex + 103, root.aegisubScaleMin, root.aegisubScaleMax) * boldScale
                                yScale: scaleFactor * root.pseudoRandom(parent.charIndex + 104, root.aegisubScaleMin, root.aegisubScaleMax) * boldScale
                                origin.x: charText.width / 2
                                origin.y: charText.height / 2
                            }
                        ]
                        
                        // Transformaciones mínimas para caracteres pendientes
                        property list<Transform> pendingTransforms: [
                            Scale {
                                xScale: 1.0
                                yScale: 1.0
                                origin.x: charText.width / 2
                                origin.y: charText.height / 2
                            }
                        ]
                        
                        // Efecto de sombra mejorado para caracteres activos
                        layer.enabled: true
                        layer.effect: DropShadow {
                            horizontalOffset: parent.isKaraoke ? 4 : 2
                            verticalOffset: parent.isKaraoke ? 4 : 2
                            radius: parent.isKaraoke ? root.aegisubBlurRadius * 6 : root.aegisubBlurRadius * 3
                            samples: parent.isKaraoke ? 17 : 9
                            color: parent.isKaraoke ? "#80000000" : "#40000000"
                        }
                        
                        // Efecto de desenfoque múltiple (simulando el blur steps del QPainter)
                        Repeater {
                            model: parent.isKaraoke ? root.aegisubMaxBlurSteps : 0
                            
                            Text {
                                text: charText.text
                                font: charText.font
                                color: charText.color
                                antialiasing: true
                                opacity: 0.2 / root.aegisubMaxBlurSteps
                                
                                // Desplazamiento circular para simular blur
                                x: Math.cos(index / root.aegisubMaxBlurSteps * Math.PI * 2) * root.aegisubBlurRadius
                                y: Math.sin(index / root.aegisubMaxBlurSteps * Math.PI * 2) * root.aegisubBlurRadius
                                
                                transform: charText.transform
                                z: -1 // Detrás del texto principal
                            }
                        }
                        
                        // Animaciones suaves para transiciones
                        Behavior on color {
                            ColorAnimation {
                                duration: 300
                                easing.type: Easing.OutQuad
                            }
                        }
                    }
                }
            }
        }
        
        // 3. TEXTO PENDIENTE con efecto 3D mínimo (como en QPainter)
        Row {
            spacing: 0
            
            Repeater {
                model: root.safePendingText.length
                
                Text {
                    property int charIndex: index
                    
                    text: (charIndex < root.safePendingText.length) ? root.safePendingText.charAt(charIndex) : ""
                    font: root.textFont
                    color: root.aegisubColorPending
                    antialiasing: true
                    
                    // Transformación mínima (como en QPainter)
                    transform: Rotation {
                        angle: 2 * Math.sin(root.constantAnimationTime + 
                               (root.safeSungText.length + root.safeCurrentSyllable.length + parent.charIndex) * 0.5)
                        axis { x: 1; y: 0; z: 0 }
                        origin.x: width / 2
                        origin.y: height / 2
                    }
                    
                    // Sombra sutil
                    layer.enabled: true
                    layer.effect: DropShadow {
                        horizontalOffset: 1
                        verticalOffset: 1
                        radius: 2
                        samples: 5
                        color: "#30000000"
                    }
                }
            }
        }
    }
    
    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }
    
    // Debug info completo (solo visible en modo debug)
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 280
        height: 140
        color: "black"
        border.color: "magenta"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode
        
        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2
            
            Text {
                text: "Aegisub 3D Effect"
                color: "magenta"
                font.pixelSize: 10
                font.bold: true
            }
            Text {
                text: "Font Size: " + root.textFont.pixelSize + "px"
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Smooth Progress: " + root.smoothProgress.toFixed(3)
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Animation Time: " + root.constantAnimationTime.toFixed(2) + "s"
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Rotation Max: " + root.aegisubRotationMax + "°"
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Scale Range: " + root.aegisubScaleMin + " - " + root.aegisubScaleMax
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Transform Speed: " + root.aegisubTransformSpeed
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Blur Steps: " + root.aegisubMaxBlurSteps
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Active Chars: " + Math.floor(root.safeCurrentSyllable.length * root.safeSyllableProgress)
                color: "magenta"
                font.pixelSize: 9
            }
        }
    }
}
