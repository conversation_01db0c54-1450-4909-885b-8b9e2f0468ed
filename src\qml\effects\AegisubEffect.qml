import QtQuick 2.15
import QtGraphicalEffects 1.15
import "../components"

EffectBase {
    id: root
    effectName: "AegisubEffect"

    // Parámetros exactos del .ass original
    property real aegisubInitialScale: 1.5     // 150% inicial (\fscy150\fscx150)
    property real aegisubFinalScale: 1.2       // 120% final (\fscy120\fscx120)
    property real aegisubEntryRotation: 360.0  // Rotación de entrada (\frz360)
    property real aegisubEntryDuration: 400    // Duración entrada (400ms)
    property real aegisubAlternateInterval: 60 // Intervalo alternancia (60ms)
    property real aegisubJitterRange: 5.0      // Jitter range (\jitter(5,5,5,5,50))
    property real aegisubParticleScale: 3.0    // Escala partículas (\fscxy(300))
    property real aegisubParticleBlur: 50      // Blur partículas (\be50)
    property real aegisubRandomRange: 200      // Rango movimiento aleatorio

    // Colores exactos del .ass (gradiente E9D8C0 a D68539)
    readonly property color aegisubGradientTop: "#E9D8C0"    // Cream
    readonly property color aegisubGradientBottom: "#D68539" // Orange
    readonly property color aegisubActiveColor: "#B3F7FF"    // Cyan (\1c&HB3F7FF&)
    readonly property color aegisubBorderColor: "#000000"    // Black border (\3c&H000000&)

    // Sistema de animación avanzado
    property real animationTime: 0.0
    property real entryAnimationTime: 0.0
    property real alternatePhase: 0.0
    property bool syllableCompleted: false
    property real particleAnimationTime: 0.0

    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS
        onTriggered: {
            animationTime += 0.016
            entryAnimationTime += 0.016
            alternatePhase += 0.016
            if (syllableCompleted) {
                particleAnimationTime += 0.016
            }
        }
    }

    // Propiedades seguras
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0

    // Funciones del .ass original
    function getAlternatingScale() {
        // AutoTags(TE3,60,'fscx100\\fscy150','fscx100\\fscy100')
        var phase = Math.floor(alternatePhase * 1000 / aegisubAlternateInterval) % 2
        if (phase === 0) {
            return { x: 1.0, y: 1.5 } // fscx100\fscy150
        } else {
            return { x: 1.0, y: 1.0 } // fscx100\fscy100
        }
    }

    function getJitterOffset() {
        // \jitter(5,5,5,5,50) - 5px jitter in all directions
        return {
            x: (Math.random() - 0.5) * aegisubJitterRange * 2,
            y: (Math.random() - 0.5) * aegisubJitterRange * 2
        }
    }

    function getRandomParticleMovement(index) {
        // math.random(0,200) para dispersión de partículas
        var seed = index + 1000 // Seed estable por índice
        var randomX = (Math.sin(seed * 12.9898) * 43758.5453) % 1
        var randomY = (Math.sin(seed * 78.233) * 43758.5453) % 1
        return {
            x: randomX * aegisubRandomRange,
            y: randomY * aegisubRandomRange
        }
    }

    // Estructura principal con efectos avanzados del .ass
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado (sin efectos especiales)
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }

        // Sílaba actual con efectos completos del .ass
        Item {
            width: Math.max(currentSyllableText.width * 2, 1)
            height: Math.max(currentSyllableText.height * 2, 1)

            // Texto base (siempre visible)
            Text {
                id: currentSyllableText
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
                anchors.centerIn: parent
            }

            // FASE 1: EFECTO DE ENTRADA (\frz360\t(0,400,\frz0))
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress > 0.0 && root.entryAnimationTime < (root.aegisubEntryDuration / 1000)

                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }

                Text {
                    id: entryText
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.aegisubActiveColor
                    antialiasing: true
                    anchors.centerIn: parent

                    // Rotación de entrada 360° → 0°
                    transform: [
                        Rotation {
                            angle: root.aegisubEntryRotation * (1.0 - Math.min(1.0, root.entryAnimationTime / (root.aegisubEntryDuration / 1000)))
                            origin.x: entryText.width / 2
                            origin.y: entryText.height / 2
                        },
                        Scale {
                            xScale: root.aegisubInitialScale
                            yScale: root.aegisubInitialScale
                            origin.x: entryText.width / 2
                            origin.y: entryText.height / 2
                        }
                    ]

                    // Fade in (\fad(400,0))
                    opacity: Math.min(1.0, root.entryAnimationTime / (root.aegisubEntryDuration / 1000))

                    // Movimiento de entrada (\move)
                    x: -10 * (1.0 - Math.min(1.0, root.entryAnimationTime / (root.aegisubEntryDuration / 1000)))
                    y: -5 * (1.0 - Math.min(1.0, root.entryAnimationTime / (root.aegisubEntryDuration / 1000)))
                }
            }

            // FASE 2: EFECTO PRINCIPAL CON ALTERNANCIA Y GRADIENTE
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress > 0.0 && root.safeSyllableProgress < 1.0 && root.entryAnimationTime >= (root.aegisubEntryDuration / 1000)

                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }

                // Texto principal con gradiente y alternancia
                Text {
                    id: mainEffectText
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    antialiasing: true
                    anchors.centerIn: parent

                    // Escalado alternante del .ass
                    property var alternateScale: root.getAlternatingScale()
                    property var jitterOffset: root.getJitterOffset()

                    transform: [
                        Scale {
                            xScale: alternateScale.x * root.aegisubInitialScale
                            yScale: alternateScale.y * root.aegisubInitialScale
                            origin.x: mainEffectText.width / 2
                            origin.y: mainEffectText.height / 2
                        }
                    ]

                    // Jitter effect (\jitter(5,5,5,5,50))
                    x: jitterOffset.x
                    y: jitterOffset.y

                    // Gradiente de color (simulando \1vc)
                    layer.enabled: true
                    layer.effect: LinearGradient {
                        start: Qt.point(0, 0)
                        end: Qt.point(0, mainEffectText.height)
                        gradient: Gradient {
                            GradientStop { position: 0.0; color: root.aegisubGradientTop }
                            GradientStop { position: 1.0; color: root.aegisubGradientBottom }
                        }
                    }
                }

                // Borde negro (\bord1.5\3c&H000000&)
                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: "transparent"
                    antialiasing: true
                    anchors.centerIn: parent

                    transform: mainEffectText.transform
                    x: mainEffectText.x
                    y: mainEffectText.y

                    layer.enabled: true
                    layer.effect: DropShadow {
                        horizontalOffset: 0
                        verticalOffset: 0
                        radius: 3
                        samples: 7
                        color: root.aegisubBorderColor
                        spread: 0.8
                    }
                }
            }

            // FASE 3: TRANSICIÓN A FINAL (\t(100$,,\fscy120\fscx120))
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress >= 1.0

                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }

                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.aegisubActiveColor
                    antialiasing: true
                    anchors.centerIn: parent

                    transform: Scale {
                        xScale: root.aegisubFinalScale
                        yScale: root.aegisubFinalScale
                        origin.x: width / 2
                        origin.y: height / 2
                    }

                    // Movimiento final (+3, +3)
                    x: 3
                    y: 3

                    // Fade out (\fad(0,250))
                    opacity: Math.max(0, 1.0 - (root.particleAnimationTime / 0.25))

                    onOpacityChanged: {
                        if (opacity <= 0) {
                            root.syllableCompleted = true
                        }
                    }
                }
            }
        }

        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }

    // FASE 4: EFECTO DE PARTÍCULAS POST-LÍNEA (\retime("postline"))
    Repeater {
        model: root.syllableCompleted ? root.safeCurrentSyllable.length : 0

        Item {
            property int charIndex: index
            property string currentChar: root.safeCurrentSyllable.length > index ? root.safeCurrentSyllable[index] : ""
            property var particleMovement: root.getRandomParticleMovement(index)
            property real particleProgress: Math.min(1.0, root.particleAnimationTime / 0.5) // 500ms duration

            anchors.centerIn: parent

            Text {
                text: parent.currentChar
                font: root.textFont
                color: root.aegisubActiveColor
                antialiasing: true

                // Movimiento aleatorio de dispersión
                x: parent.particleMovement.x * parent.particleProgress
                y: parent.particleMovement.y * parent.particleProgress

                // Escalado extremo (\fscxy(300))
                transform: [
                    Scale {
                        xScale: 1.0 + (root.aegisubParticleScale - 1.0) * parent.particleProgress
                        yScale: 1.0 + (root.aegisubParticleScale - 1.0) * parent.particleProgress
                        origin.x: width / 2
                        origin.y: height / 2
                    }
                ]

                // Fade out (\fad(0,500))
                opacity: Math.max(0, 1.0 - parent.particleProgress)

                // Blur effect (\be50)
                layer.enabled: true
                layer.effect: FastBlur {
                    radius: root.aegisubParticleBlur * parent.particleProgress
                }

                // Random positioning (\rnd20)
                rotation: 20 * (Math.random() - 0.5) * parent.particleProgress
            }
        }
    }

    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }

    // Debug info avanzado
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 280
        height: 120
        color: "black"
        border.color: "magenta"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode

        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2

            Text {
                text: "Aegisub Effect - 100% Fidelity"
                color: "white"
                font.pixelSize: 10
                font.bold: true
            }
            Text {
                text: "Entry Time: " + root.entryAnimationTime.toFixed(2) + "s"
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Alternate Phase: " + (root.alternatePhase * 1000 / root.aegisubAlternateInterval).toFixed(1)
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Particle Time: " + root.particleAnimationTime.toFixed(2) + "s"
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Completed: " + (root.syllableCompleted ? "YES" : "NO")
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "yellow"
                font.pixelSize: 9
            }
            Text {
                text: "Current: '" + root.safeCurrentSyllable + "'"
                color: "lightgreen"
                font.pixelSize: 9
            }
            Text {
                text: "Effects: Entry→Main→Final→Particles"
                color: "orange"
                font.pixelSize: 9
            }
        }
    }
}
