import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "AegisubEffect"

    // Parámetros del efecto Aegisub (basados en el .ass original)
    property real aegisubInitialScale: 1.5     // Escalado inicial (150% del .ass)
    property real aegisubFinalScale: 1.2       // Escalado final (120% del .ass)
    property real aegisubRotationRange: 10.0   // Rango de rotación
    property real aegisubMoveDistance: 5.0     // Distancia de movimiento

    // Colores del efecto Aegisub
    readonly property color aegisubActiveColor: Qt.rgba(0.8, 1.0, 0.9, 1.0)  // Verde claro brillante
    readonly property color aegisubGlowColor: Qt.rgba(0.9, 0.8, 1.0, 1.0)   // Púrpura suave

    // Animación continua
    property real animationTime: 0.0

    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS
        onTriggered: animationTime += 0.016
    }

    // Propiedades seguras (siguiendo el patrón exitoso)
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0

    // Estructura principal (siguiendo el patrón exitoso)
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0

        // Texto cantado (sin efectos)
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }

        // Sílaba actual con efecto Aegisub (siguiendo el patrón exitoso)
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height

            // Texto de fondo (color pendiente) - siempre visible como base
            Text {
                id: currentSyllableText
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }

            // Contenedor para la sílaba con efecto Aegisub (solo cuando hay progreso)
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress > 0.0 && root.safeSyllableProgress < 1.0

                // Fondo negro para borrar el texto base (como en QPainter)
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }

                // Sílaba con efecto Aegisub - PASO 1: Escalado dinámico del .ass
                Text {
                    id: aegisubSyllable
                    anchors.centerIn: parent
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.aegisubActiveColor
                    antialiasing: true

                    // Escalado dinámico basado en el .ass original (150% → 120%)
                    property real dynamicScale: {
                        var progress = root.safeSyllableProgress
                        if (progress < 0.5) {
                            // Primera mitad: escalar de 1.0 a 1.5
                            return 1.0 + (root.aegisubInitialScale - 1.0) * (progress * 2)
                        } else {
                            // Segunda mitad: escalar de 1.5 a 1.2
                            return root.aegisubInitialScale - (root.aegisubInitialScale - root.aegisubFinalScale) * ((progress - 0.5) * 2)
                        }
                    }

                    transform: [
                        Scale {
                            xScale: aegisubSyllable.dynamicScale
                            yScale: aegisubSyllable.dynamicScale
                            origin.x: aegisubSyllable.width / 2
                            origin.y: aegisubSyllable.height / 2
                        },
                        Rotation {
                            angle: root.aegisubRotationRange * Math.sin(root.animationTime * 2) * root.safeSyllableProgress
                            origin.x: aegisubSyllable.width / 2
                            origin.y: aegisubSyllable.height / 2
                        }
                    ]

                    // Movimiento sutil (del .ass original)
                    x: root.aegisubMoveDistance * Math.sin(root.animationTime * 1.5) * root.safeSyllableProgress
                    y: root.aegisubMoveDistance * Math.cos(root.animationTime * 1.5) * root.safeSyllableProgress

                    // Animación suave
                    Behavior on dynamicScale {
                        PropertyAnimation {
                            duration: 100
                            easing.type: Easing.OutQuad
                        }
                    }
                }

                // Efecto de brillo (del .ass original)
                Text {
                    anchors.centerIn: parent
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.aegisubGlowColor
                    antialiasing: true
                    opacity: 0.6 * root.safeSyllableProgress

                    // Replicar las transformaciones del texto principal
                    transform: [
                        Scale {
                            xScale: aegisubSyllable.dynamicScale * 1.1
                            yScale: aegisubSyllable.dynamicScale * 1.1
                            origin.x: width / 2
                            origin.y: height / 2
                        },
                        Rotation {
                            angle: root.aegisubRotationRange * Math.sin(root.animationTime * 2) * root.safeSyllableProgress
                            origin.x: width / 2
                            origin.y: height / 2
                        }
                    ]

                    // Movimiento sutil (del .ass original)
                    x: root.aegisubMoveDistance * Math.sin(root.animationTime * 1.5) * root.safeSyllableProgress
                    y: root.aegisubMoveDistance * Math.cos(root.animationTime * 1.5) * root.safeSyllableProgress
                }
            }

            // Sílaba completada (tamaño final, color cantado)
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress >= 1.0

                // Fondo negro para borrar el texto base
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }

                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                    scale: root.aegisubFinalScale
                    transformOrigin: Item.Center
                }
            }
        }

        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }

    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }

    // Debug info (siguiendo el patrón exitoso)
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 220
        height: 80
        color: "black"
        border.color: "magenta"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode

        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2

            Text {
                text: "Aegisub Effect - PASO 1"
                color: "white"
                font.pixelSize: 10
                font.bold: true
            }
            Text {
                text: "Initial Scale: " + root.aegisubInitialScale.toFixed(2)
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Final Scale: " + root.aegisubFinalScale.toFixed(2)
                color: "magenta"
                font.pixelSize: 9
            }
            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "cyan"
                font.pixelSize: 9
            }
            Text {
                text: "Current: '" + root.safeCurrentSyllable + "'"
                color: "lightgreen"
                font.pixelSize: 9
            }
        }
    }
}
