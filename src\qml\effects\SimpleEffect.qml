import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "SimpleEffect"
    
    // Contenido principal del efecto
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0
        
        // Texto cantado
        Text {
            text: root.sungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }
        
        // Sílaba actual con progreso de recorte
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height
            
            Text {
                id: currentSyllableText
                text: root.currentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }
            
            Rectangle {
                width: parent.width * root.syllableProgress
                height: parent.height
                color: "transparent"
                clip: true
                
                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                }
            }
        }
        
        // Texto pendiente
        Text {
            text: root.pendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }
    
    // <PERSON>ínea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.nextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }
}
