import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs
import UltraStarBasics 1.0

ApplicationWindow {
    id: window
    width: 800
    height: 600
    visible: true
    title: "UltraStar Simple"

    // Propiedades locales para evitar binding loops
    property var localMainBackend: mainBackend
    property var localKaraokeBackend: karaokeBackend

    // Verificación inicial simple
    Component.onCompleted: {
        if (localKaraokeBackend) {
            console.log("✅ UltraStar Simple: Backends connected successfully")
        } else {
            console.log("❌ UltraStar Simple: Backend connection failed")
        }
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10

        RowLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true

            // Song list
            GroupBox {
                title: "Songs"
                Layout.preferredWidth: 300
                Layout.fillHeight: true

                ListView {
                    id: songListView
                    anchors.fill: parent
                    model: localMainBackend ? localMainBackend.songList : []

                    delegate: ItemDelegate {
                        width: songListView.width
                        height: 40

                        Rectangle {
                            anchors.fill: parent
                            color: parent.hovered ? "#e0e0e0" : "transparent"
                            border.color: "#ccc"
                            border.width: 1

                            Text {
                                anchors.left: parent.left
                                anchors.leftMargin: 10
                                anchors.verticalCenter: parent.verticalCenter
                                text: modelData.artist + " - " + modelData.title
                                elide: Text.ElideRight
                            }
                        }

                        onClicked: {
                            if (localMainBackend) {
                                localMainBackend.select_song(modelData.index)
                            }
                        }
                    }
                }
            }

            // Info and controls
            ColumnLayout {
                Layout.fillWidth: true
                Layout.fillHeight: true

                // Song info
                GroupBox {
                    title: "Song Information"
                    Layout.fillWidth: true

                    RowLayout {
                        anchors.fill: parent

                        Text {
                            id: songInfoText
                            Layout.fillWidth: true
                            text: localMainBackend ? localMainBackend.currentSongInfo : "No backend"
                            wrapMode: Text.WordWrap
                        }

                        Rectangle {
                            Layout.preferredWidth: 200
                            Layout.preferredHeight: 200
                            color: "#333"
                            border.color: "#666"
                            border.width: 1

                            Image {
                                id: coverImage
                                anchors.fill: parent
                                anchors.margins: 2
                                source: localMainBackend ? localMainBackend.currentCoverPath : ""
                                fillMode: Image.PreserveAspectFit
                                visible: source != ""
                            }

                            Text {
                                anchors.centerIn: parent
                                text: "No Cover"
                                color: "#999"
                                visible: !localMainBackend || !localMainBackend.currentCoverPath || coverImage.status === Image.Error
                            }
                        }
                    }
                }

                // Playback controls
                GroupBox {
                    title: "Playback Controls"
                    Layout.fillWidth: true

                    ColumnLayout {
                        anchors.fill: parent

                        RowLayout {
                            Layout.fillWidth: true

                            Button {
                                text: "Play"
                                onClicked: {
                                    if (localMainBackend) {
                                        localMainBackend.play()
                                    }
                                }
                            }

                            Button {
                                text: "Pause"
                                onClicked: {
                                    if (localMainBackend) {
                                        localMainBackend.pause()
                                    }
                                }
                            }

                            Button {
                                text: "Stop"
                                onClicked: {
                                    if (localMainBackend) {
                                        localMainBackend.stop()
                                    }
                                }
                            }

                            Button {
                                text: "Load Songs"
                                onClicked: {
                                    if (localMainBackend) {
                                        localMainBackend.load_songs_folder()
                                    }
                                }
                            }
                        }

                        Slider {
                            id: progressSlider
                            Layout.fillWidth: true
                            from: 0
                            to: 1
                            value: (localMainBackend && localMainBackend.duration > 0) ? localMainBackend.position / localMainBackend.duration : 0

                            onMoved: {
                                if (localMainBackend) {
                                    localMainBackend.seek(value)
                                }
                            }
                        }

                        Text {
                            Layout.alignment: Qt.AlignHCenter
                            text: localMainBackend ? 
                                  (localMainBackend.formatTime(localMainBackend.position) + " / " + localMainBackend.formatTime(localMainBackend.duration)) :
                                  "00:00 / 00:00"
                        }
                    }
                }

                // Karaoke options
                GroupBox {
                    title: "Karaoke Options"
                    Layout.fillWidth: true

                    ColumnLayout {
                        anchors.fill: parent

                        // Color controls
                        RowLayout {
                            Layout.fillWidth: true

                            Button {
                                text: "Sung Color"
                                onClicked: sungColorDialog.open()
                            }

                            Button {
                                text: "Pending Color"
                                onClicked: pendingColorDialog.open()
                            }

                            Button {
                                text: "Ball Color"
                                onClicked: ballColorDialog.open()
                            }
                        }

                        // Effect selection
                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "Effect:"
                            }

                            ComboBox {
                                id: effectComboBox
                                Layout.fillWidth: true
                                model: ["Simple", "Zoom", "Particle", "Ball", "Shift", "Wave", "Pulse", "Typewriter", "Aegisub 3D", "World Wave"]  // ← AGREGADO "World Wave"
                                currentIndex: localKaraokeBackend ? localKaraokeBackend.currentEffect : 0

                                onCurrentIndexChanged: {
                                    if (localKaraokeBackend) {
                                        localKaraokeBackend.setEffect(currentIndex)
                                    }
                                }
                            }
                        }

                        // Settings
                        GridLayout {
                            Layout.fillWidth: true
                            columns: 2

                            Text { text: "Transition Effect:" }
                            CheckBox {
                                checked: localKaraokeBackend ? localKaraokeBackend.transitionEffect : false
                                onCheckedChanged: {
                                    if (localKaraokeBackend) {
                                        localKaraokeBackend.transitionEffect = checked
                                    }
                                }
                            }

                            Text { text: "Interpolation:" }
                            CheckBox {
                                checked: localKaraokeBackend ? localKaraokeBackend.interpolation : false
                                onCheckedChanged: {
                                    if (localKaraokeBackend) {
                                        localKaraokeBackend.interpolation = checked
                                    }
                                }
                            }

                            Text { text: "Transition Speed:" }
                            Slider {
                                Layout.fillWidth: true
                                from: 1
                                to: 10
                                value: localKaraokeBackend ? localKaraokeBackend.transitionSpeed : 5
                                onValueChanged: {
                                    if (localKaraokeBackend) {
                                        localKaraokeBackend.transitionSpeed = value
                                    }
                                }
                            }

                            Text { text: "Sync Offset (ms):" }
                            Slider {
                                Layout.fillWidth: true
                                from: -100
                                to: 100
                                value: localKaraokeBackend ? localKaraokeBackend.syncOffset * 1000 : 0
                                onValueChanged: {
                                    if (localKaraokeBackend) {
                                        localKaraokeBackend.syncOffset = value / 1000
                                    }
                                }
                            }

                            Text { text: "Smoothing:" }
                            Slider {
                                Layout.fillWidth: true
                                from: 0
                                to: 1
                                value: localKaraokeBackend ? localKaraokeBackend.smoothingFactor : 0.5
                                onValueChanged: {
                                    if (localKaraokeBackend) {
                                        localKaraokeBackend.smoothingFactor = value
                                    }
                                }
                            }

                            Text { text: "Anticipation (ms):" }
                            Slider {
                                Layout.fillWidth: true
                                from: 0
                                to: 50
                                value: localKaraokeBackend ? localKaraokeBackend.anticipationTime * 1000 : 0
                                onValueChanged: {
                                    if (localKaraokeBackend) {
                                        localKaraokeBackend.anticipationTime = value / 1000
                                    }
                                }
                            }
                        }

                        Button {
                            text: "Debug Info"
                            onClicked: debugDialog.open()
                        }
                    }
                }
            }
        }

        // Karaoke display
        GroupBox {
            title: "Karaoke Display"
            Layout.fillWidth: true
            Layout.preferredHeight: 200

            Rectangle {
                anchors.fill: parent
                color: "black"

                // Opción para usar efectos QML
                CheckBox {
                    id: useQmlEffects
                    anchors.top: parent.top
                    anchors.right: parent.right
                    anchors.margins: 5
                    text: "QML Effects"
                    checked: false
                    z: 10
                }

                // Sistema QML (experimental)
                KaraokeDisplay {
                    id: qmlKaraokeDisplay
                    anchors.fill: parent
                    karaokeBackend: localKaraokeBackend
                    visible: useQmlEffects.checked
                }

                // Sistema original (por defecto)
                KaraokeItem {
                    id: originalKaraokeDisplay
                    anchors.fill: parent
                    backend: localKaraokeBackend
                    visible: !useQmlEffects.checked
                }

                // Texto de fallback si no hay backend
                Text {
                    anchors.centerIn: parent
                    text: "❌ No Karaoke Backend Available"
                    color: "white"
                    visible: !localKaraokeBackend
                    font.pixelSize: 16
                }
            }
        }
    }

    // Color dialogs
    ColorDialog {
        id: sungColorDialog
        title: "Select Sung Text Color"
        selectedColor: localKaraokeBackend ? localKaraokeBackend.sungColor : "blue"
        onAccepted: {
            if (localKaraokeBackend) {
                localKaraokeBackend.sungColor = selectedColor
            }
        }
    }

    ColorDialog {
        id: pendingColorDialog
        title: "Select Pending Text Color"
        selectedColor: localKaraokeBackend ? localKaraokeBackend.pendingColor : "black"
        onAccepted: {
            if (localKaraokeBackend) {
                localKaraokeBackend.pendingColor = selectedColor
            }
        }
    }

    ColorDialog {
        id: ballColorDialog
        title: "Select Ball Color"
        selectedColor: localKaraokeBackend ? localKaraokeBackend.ballColor : "red"
        onAccepted: {
            if (localKaraokeBackend) {
                localKaraokeBackend.ballColor = selectedColor
            }
        }
    }

    // Debug dialog
    Dialog {
        id: debugDialog
        title: "Debug Information"
        width: 500
        height: 400

        ScrollView {
            anchors.fill: parent
            TextArea {
                text: localKaraokeBackend ? localKaraokeBackend.getDebugInfo() : "No debug info available"
                readOnly: true
                wrapMode: TextArea.Wrap
            }
        }
    }
}
