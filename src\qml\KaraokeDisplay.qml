import QtQuick 2.15
import UltraStarBasics 1.0
import "effects"

Rectangle {
    id: root
    
    property var karaokeBackend
    color: "black"
    
    // Estado simple
    property bool debugMode: false
    property int effectsLoaded: 0
    
    // Verificación inicial del backend
    Component.onCompleted: {
        if (karaokeBackend) {
            console.log("✅ KaraokeDisplay: Backend connected successfully")
        } else {
            console.log("❌ KaraokeDisplay: No backend available")
        }
    }
    
    onKaraokeBackendChanged: {
        if (karaokeBackend) {
            console.log("✅ KaraokeDisplay: Backend updated successfully")
        }
    }
    
    // Botón de debug (oculto por defecto)
    Rectangle {
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 5
        width: 40
        height: 15
        color: debugMode ? "green" : "gray"
        opacity: 0.3
        z: 1000
        
        Text {
            anchors.centerIn: parent
            text: "DBG"
            color: "white"
            font.pixelSize: 8
        }
        
        MouseArea {
            anchors.fill: parent
            onClicked: {
                debugMode = !debugMode
                console.log(`Debug mode ${debugMode ? "enabled" : "disabled"}`)
            }
        }
    }
    
    // Información de debug (solo visible cuando se activa)
    Text {
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.margins: 5
        color: "yellow"
        font.pixelSize: 10
        text: {
            if (!karaokeBackend) return "❌ No backend"
            
            try {
                let status = "✅ Backend OK\n"
                status += `Effect: ${karaokeBackend.getEffectName()}\n`
                status += `Effects loaded: ${effectsLoaded}\n`
                status += `Has data: ${karaokeBackend.hasValidData()}`
                return status
            } catch (e) {
                return `❌ Backend error: ${e}`
            }
        }
        visible: debugMode
        z: 1000
    }
    
    // Effect loader simplificado
    Loader {
        id: effectLoader
        anchors.fill: parent
        
        property string currentEffectSource: {
            if (!karaokeBackend) {
                return "effects/SimpleEffect.qml"
            }
            
            let effectMap = [
                "effects/SimpleEffect.qml",      // 0
                "effects/ZoomEffect.qml",        // 1
                "effects/ParticleEffect.qml",    // 2
                "effects/BallEffect.qml",        // 3
                "effects/ShiftEffect.qml",       // 4
                "effects/WaveEffect.qml",        // 5
                "effects/PulseEffect.qml",       // 6
                "effects/TypewriterEffect.qml",  // 7
                "effects/AegisubEffect.qml",     // 8
                "effects/WorldWaveEffect.qml"    // 9 ← AGREGADO NUEVO EFECTO
            ]
            
            try {
                let effectIndex = karaokeBackend.currentEffect
                if (effectIndex >= 0 && effectIndex < effectMap.length) {
                    return effectMap[effectIndex]
                }
                return "effects/SimpleEffect.qml"
            } catch (e) {
                return "effects/SimpleEffect.qml"
            }
        }
        
        source: currentEffectSource
        
        onLoaded: {
            effectsLoaded++
            
            if (item && karaokeBackend) {
                try {
                    if (item.hasOwnProperty('backend')) {
                        item.backend = karaokeBackend
                        if (debugMode) {
                            console.log(`✅ Effect loaded: ${karaokeBackend.getEffectName()}`)
                        }
                    }
                } catch (e) {
                    console.log("❌ Error assigning backend:", e)
                }
            }
        }
        
        onStatusChanged: {
            if (status === Loader.Error) {
                console.log("❌ Error loading effect, using fallback")
                if (source !== "effects/SimpleEffect.qml") {
                    source = "effects/SimpleEffect.qml"
                }
            }
        }
        
        // Conexiones para cambios de efecto
        Connections {
            target: karaokeBackend
            enabled: karaokeBackend !== null && karaokeBackend !== undefined
            
            function onCurrentEffectChanged() {
                effectLoader.source = ""
                reloadTimer.restart()
            }
        }
        
        // Timer para recargar efectos
        Timer {
            id: reloadTimer
            interval: 50
            onTriggered: {
                effectLoader.source = effectLoader.currentEffectSource
            }
        }
    }
    
    // Texto de fallback
    Text {
        anchors.centerIn: parent
        text: {
            if (!karaokeBackend) return "❌ No Backend Connected"
            if (effectLoader.status === Loader.Loading) return "⏳ Loading Effect..."
            if (effectLoader.status === Loader.Error) return "❌ Effect Error"
            if (!effectLoader.item) return "❌ No Effect Loaded"
            return "❌ Unknown State"
        }
        color: "white"
        visible: !effectLoader.item || effectLoader.status === Loader.Error
        horizontalAlignment: Text.AlignHCenter
        font.pixelSize: 16
    }
    
    // Indicador de sistema QML (solo cuando debug está activo)
    Text {
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.margins: 5
        color: "cyan"
        text: `QML System (${effectsLoaded} effects loaded)`
        font.pixelSize: 10
        visible: debugMode
        z: 999
    }
}
