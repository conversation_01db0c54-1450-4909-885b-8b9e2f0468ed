#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtQml import QmlElement, qmlRegisterType, QQmlApplicationEngine
from PySide6.QtCore import QUrl, QSize

# Import backend classes
from src.backend.main_backend import MainBackend
from src.backend.karaoke_backend import KaraokeBackend
from src.ui.karaoke_item import KaraokeItem

# QML type registration
QML_IMPORT_NAME = "UltraStarBasics"
QML_IMPORT_MAJOR_VERSION = 1

def main():
    """Main function"""
    # Create the application
    app = QApplication(sys.argv)
    app.setApplicationName("UltraStar Simple")

    # Register QML types
    qmlRegisterType(KaraokeItem, QML_IMPORT_NAME, QML_IMPORT_MAJOR_VERSION, 0, "KaraokeItem")

    # Create the QML engine
    engine = QQmlApplicationEngine()
    
    # Create backend instances
    main_backend = MainBackend()
    karaoke_backend = KaraokeBackend()
    
    # Connect backends
    main_backend.set_karaoke_backend(karaoke_backend)
    
    # Expose backends to QML
    engine.rootContext().setContextProperty("mainBackend", main_backend)
    engine.rootContext().setContextProperty("karaokeBackend", karaoke_backend)
    
    # Load QML file
    qml_file = os.path.join(os.path.dirname(__file__), "qml", "main.qml")
    engine.load(QUrl.fromLocalFile(qml_file))

    # Check if QML loaded successfully
    if not engine.rootObjects():
        return -1

    # Load songs if specified as argument
    if len(sys.argv) > 1 and os.path.isdir(sys.argv[1]):
        main_backend.load_songs(sys.argv[1])
    else:
        # Try to load songs from the "songs" folder
        songs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'songs')
        if os.path.isdir(songs_dir):
            main_backend.load_songs(songs_dir)

    # Execute the application
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
