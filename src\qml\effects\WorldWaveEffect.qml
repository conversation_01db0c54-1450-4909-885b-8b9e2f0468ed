import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "WorldWaveEffect"
    
    // Parámetros del efecto de onda por palabras
    property real wordDelay: 0.1  // Delay entre palabras (en segundos)
    property real waveAmplitude: 8.0  // Amplitud de la onda vertical
    property real waveSpeed: 4.0  // Velocidad de la animación
    property real scaleEffect: 1.3  // Factor de escala para palabras activas
    property real glowIntensity: 0.6  // Intensidad del efecto de brillo
    
    // Estados de las líneas
    property real inactiveOpacity: 0.4
    property real activeOpacity: 1.0
    property real inactiveScale: 0.95
    property real activeScale: 1.05
    
    // Timer para animaciones constantes
    property real animationTime: 0.0
    
    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS
        onTriggered: {
            root.animationTime += 0.016
        }
    }
    
    // Propiedades seguras
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0
    
    // Función para dividir texto en palabras
    function getWords(text) {
        if (!text) return []
        return text.split(' ').filter(function(word) { return word.length > 0 })
    }
    
    // Función para determinar si una palabra debe estar activa
    function isWordActive(wordIndex, totalWords, progress) {
        if (totalWords <= 1) return progress > 0
        var wordThreshold = wordIndex / (totalWords - 1)
        return progress >= wordThreshold
    }
    
    // Función para calcular el progreso de animación de una palabra
    function getWordAnimationProgress(wordIndex, totalWords, progress) {
        if (totalWords <= 1) return progress
        var wordThreshold = wordIndex / (totalWords - 1)
        var nextThreshold = (wordIndex + 1) / (totalWords - 1)
        
        if (progress < wordThreshold) return 0.0
        if (progress >= nextThreshold) return 1.0
        
        return (progress - wordThreshold) / (nextThreshold - wordThreshold)
    }
    
    // Contenedor principal
    Column {
        anchors.centerIn: parent
        spacing: 15
        
        // Línea actual con efecto de onda por palabras
        Item {
            width: currentLineRow.width
            height: currentLineRow.height + root.waveAmplitude * 2
            anchors.horizontalCenter: parent.horizontalCenter
            
            Row {
                id: currentLineRow
                spacing: 8
                anchors.verticalCenter: parent.verticalCenter
                
                // Texto cantado (palabras completamente activas)
                Repeater {
                    model: root.getWords(root.safeSungText)
                    
                    Item {
                        property string word: modelData
                        property int wordIndex: index
                        property real wavePhase: root.animationTime * root.waveSpeed + wordIndex * 0.8
                        
                        width: wordText.width
                        height: wordText.height
                        
                        // Efecto de onda vertical
                        transform: Translate {
                            y: root.waveAmplitude * 0.3 * Math.sin(wavePhase)
                        }
                        
                        Text {
                            id: wordText
                            text: parent.word + " "
                            font.family: root.textFont.family
                            font.pixelSize: root.textFont.pixelSize
                            font.weight: Font.Bold
                            color: root.sungColor
                            antialiasing: true
                            scale: root.activeScale
                            opacity: root.activeOpacity
                            
                            // Efecto de brillo simulado con múltiples capas
                            Rectangle {
                                anchors.centerIn: parent
                                width: parent.width + 4
                                height: parent.height + 4
                                color: "transparent"
                                border.color: Qt.lighter(root.sungColor, 1.5)
                                border.width: 1
                                opacity: root.glowIntensity * 0.3
                                radius: 2
                                z: -1
                            }
                            
                            Rectangle {
                                anchors.centerIn: parent
                                width: parent.width + 8
                                height: parent.height + 8
                                color: "transparent"
                                border.color: Qt.lighter(root.sungColor, 1.3)
                                border.width: 1
                                opacity: root.glowIntensity * 0.2
                                radius: 3
                                z: -2
                            }
                        }
                    }
                }
                
                // Sílaba actual (con animación progresiva por palabras)
                Repeater {
                    model: root.getWords(root.safeCurrentSyllable)
                    
                    Item {
                        property string word: modelData
                        property int wordIndex: index
                        property int totalCurrentWords: root.getWords(root.safeCurrentSyllable).length
                        property bool isActive: root.isWordActive(wordIndex, totalCurrentWords, root.safeSyllableProgress)
                        property real animationProgress: root.getWordAnimationProgress(wordIndex, totalCurrentWords, root.safeSyllableProgress)
                        property real wavePhase: root.animationTime * root.waveSpeed * (1 + animationProgress) + wordIndex * 1.2
                        
                        width: wordText.width
                        height: wordText.height
                        
                        // Efecto de onda más pronunciado para palabras activas
                        transform: [
                            Translate {
                                y: parent.isActive ? 
                                   root.waveAmplitude * Math.sin(parent.wavePhase) * parent.animationProgress :
                                   0
                            },
                            Scale {
                                xScale: parent.isActive ? 
                                       (root.inactiveScale + (root.scaleEffect - root.inactiveScale) * parent.animationProgress) :
                                       root.inactiveScale
                                yScale: parent.isActive ? 
                                       (root.inactiveScale + (root.scaleEffect - root.inactiveScale) * parent.animationProgress) :
                                       root.inactiveScale
                                origin.x: wordText.width / 2
                                origin.y: wordText.height / 2
                            }
                        ]
                        
                        Text {
                            id: wordText
                            text: parent.word + " "
                            font.family: root.textFont.family
                            font.pixelSize: root.textFont.pixelSize
                            font.weight: parent.isActive ? Font.Bold : Font.Normal
                            antialiasing: true
                            
                            // Color y opacidad basados en estado y progreso
                            color: parent.isActive ? 
                                   Qt.rgba(
                                       root.sungColor.r,
                                       root.sungColor.g, 
                                       root.sungColor.b,
                                       root.inactiveOpacity + (root.activeOpacity - root.inactiveOpacity) * parent.animationProgress
                                   ) : root.pendingColor
                            
                            opacity: parent.isActive ? 
                                    (root.inactiveOpacity + (root.activeOpacity - root.inactiveOpacity) * parent.animationProgress) :
                                    root.inactiveOpacity
                            
                            // Efecto de brillo para palabras activas
                            Rectangle {
                                anchors.centerIn: parent
                                width: parent.width + 6
                                height: parent.height + 6
                                color: "transparent"
                                border.color: Qt.lighter(root.sungColor, 1.8)
                                border.width: 2
                                opacity: parent.parent.isActive ? 
                                        root.glowIntensity * parent.parent.animationProgress * 0.5 : 0
                                radius: 3
                                z: -1
                                
                                // Animación de pulso para el brillo
                                SequentialAnimation on opacity {
                                    running: parent.parent.isActive && parent.parent.animationProgress > 0.5
                                    loops: Animation.Infinite
                                    NumberAnimation {
                                        from: parent.parent.parent.isActive ? 
                                              root.glowIntensity * parent.parent.parent.animationProgress * 0.5 : 0
                                        to: parent.parent.parent.isActive ? 
                                            root.glowIntensity * parent.parent.parent.animationProgress * 0.8 : 0
                                        duration: 800
                                        easing.type: Easing.InOutSine
                                    }
                                    NumberAnimation {
                                        from: parent.parent.parent.isActive ? 
                                              root.glowIntensity * parent.parent.parent.animationProgress * 0.8 : 0
                                        to: parent.parent.parent.isActive ? 
                                            root.glowIntensity * parent.parent.parent.animationProgress * 0.5 : 0
                                        duration: 800
                                        easing.type: Easing.InOutSine
                                    }
                                }
                            }
                            
                            // Transiciones suaves
                            Behavior on opacity {
                                NumberAnimation { duration: 300; easing.type: Easing.OutQuad }
                            }
                            
                            Behavior on color {
                                ColorAnimation { duration: 300; easing.type: Easing.OutQuad }
                            }
                        }
                    }
                }
                
                // Texto pendiente (palabras inactivas)
                Repeater {
                    model: root.getWords(root.safePendingText)
                    
                    Item {
                        property string word: modelData
                        property int wordIndex: index
                        
                        width: wordText.width
                        height: wordText.height
                        
                        Text {
                            id: wordText
                            text: parent.word + " "
                            font.family: root.textFont.family
                            font.pixelSize: root.textFont.pixelSize
                            font.weight: Font.Normal
                            color: root.pendingColor
                            opacity: root.inactiveOpacity
                            scale: root.inactiveScale
                            antialiasing: true
                            
                            Behavior on opacity {
                                NumberAnimation { duration: 300 }
                            }
                        }
                    }
                }
            }
        }
        
        // Línea siguiente
        Text {
            anchors.horizontalCenter: parent.horizontalCenter
            text: root.safeNextLineText
            font: root.nextLineFont
            color: root.nextLineColor
            opacity: root.inactiveOpacity
            antialiasing: true
            visible: text.length > 0
            
            // Sutil efecto de onda para la línea siguiente
            transform: Translate {
                y: 2 * Math.sin(root.animationTime * 2)
            }
        }
    }
    
    // Debug info
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 300
        height: 140
        color: "black"
        border.color: "purple"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode
        
        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2
            
            Text {
                text: "World Wave Effect"
                color: "purple"
                font.pixelSize: 10
                font.bold: true
            }
            Text {
                text: "Word Delay: " + root.wordDelay + "s"
                color: "purple"
                font.pixelSize: 9
            }
            Text {
                text: "Wave Amplitude: " + root.waveAmplitude + "px"
                color: "purple"
                font.pixelSize: 9
            }
            Text {
                text: "Wave Speed: " + root.waveSpeed
                color: "purple"
                font.pixelSize: 9
            }
            Text {
                text: "Scale Effect: " + root.scaleEffect
                color: "purple"
                font.pixelSize: 9
            }
            Text {
                text: "Sung Words: " + root.getWords(root.safeSungText).length
                color: "purple"
                font.pixelSize: 9
            }
            Text {
                text: "Current Words: " + root.getWords(root.safeCurrentSyllable).length
                color: "purple"
                font.pixelSize: 9
            }
            Text {
                text: "Pending Words: " + root.getWords(root.safePendingText).length
                color: "purple"
                font.pixelSize: 9
            }
            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "purple"
                font.pixelSize: 9
            }
            Text {
                text: "Animation Time: " + root.animationTime.toFixed(2) + "s"
                color: "purple"
                font.pixelSize: 9
            }
        }
    }
}
