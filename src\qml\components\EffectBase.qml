import QtQuick 2.15

// Componente base para todos los efectos de karaoke
Item {
    id: effectBase
    
    // Propiedades estándar que todos los efectos deben tener
    property var backend: null
    property bool debugMode: false
    property string effectName: "BaseEffect"
    
    // Propiedades de texto (con valores por defecto seguros)
    readonly property string sungText: backend ? (backend.sungText || "") : ""
    readonly property string currentSyllable: backend ? (backend.currentSyllable || "") : ""
    readonly property string pendingText: backend ? (backend.pendingText || "") : ""
    readonly property string nextLineText: backend ? (backend.nextLineText || "") : ""
    readonly property real syllableProgress: backend ? (backend.syllableProgress || 0.0) : 0.0
    
    // Propiedades de colores (con valores por defecto seguros)
    readonly property color sungColor: backend ? backend.sungColor : "#00ff00"
    readonly property color pendingColor: backend ? backend.pendingColor : "#ffffff"
    readonly property color nextLineColor: backend ? backend.nextLineColor : "#808080"
    readonly property color ballColor: backend ? backend.ballColor : "#ffff00"
    
    // Propiedades de configuración
    readonly property bool transitionEffect: backend ? backend.transitionEffect : true
    readonly property bool interpolation: backend ? backend.interpolation : true
    readonly property real transitionSpeed: backend ? backend.transitionSpeed : 6.0
    readonly property real syncOffset: backend ? backend.syncOffset : 0.0
    readonly property real smoothingFactor: backend ? backend.smoothingFactor : 0.5
    readonly property real anticipationTime: backend ? backend.anticipationTime : 0.022
    
    // Fuente estándar
    readonly property font textFont: Qt.font({
        family: "Arial",
        pixelSize: 24,
        bold: true
    })
    
    readonly property font nextLineFont: Qt.font({
        family: "Arial", 
        pixelSize: 18,
        bold: false
    })
    
    // Función de validación
    function isValid() {
        return backend !== null && backend !== undefined
    }
    
    // Validación inicial (solo mensaje esencial)
    Component.onCompleted: {
        if (!isValid()) {
            console.log(`❌ ${effectName}: No backend assigned`)
        }
    }
    
    onBackendChanged: {
        if (isValid()) {
            // Solo log si debug está habilitado globalmente
            if (debugMode) {
                console.log(`✅ ${effectName}: Backend connected`)
            }
        }
    }
}
