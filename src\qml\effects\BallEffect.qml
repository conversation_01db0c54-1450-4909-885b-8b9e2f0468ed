import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "BallEffect"
    
    // Parámetros del efecto ball (adaptables al tamaño del texto)
    property real ballSizeRatio: 0.75        // Tamaño de la pelota como ratio del tamaño de fuente
    property real ballOffsetRatio: 2.5       // Offset como ratio del tamaño de fuente
    property real ballSmoothness: 0.8
    property real ballSquash: 0.25
    property real shadowAlpha: 120
    property real highlightSize: 0.4
    
    // Propiedades seguras
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0
    
    // TextMetrics para cálculos precisos
    TextMetrics {
        id: sungTextMetrics
        font: root.textFont
        text: root.safeSungText
    }
    
    TextMetrics {
        id: currentSyllableMetrics
        font: root.textFont
        text: root.safeCurrentSyllable
    }
    
    // Tamaños adaptativos basados en el tamaño de fuente
    readonly property real adaptiveBallSize: root.textFont.pixelSize * ballSizeRatio
    readonly property real adaptiveBallOffset: root.textFont.pixelSize * ballOffsetRatio
    readonly property real adaptiveBounceHeight: root.textFont.pixelSize * 2.0
    
    // Cálculo preciso de la posición del progreso
    readonly property real ballXPosition: sungTextMetrics.width + (currentSyllableMetrics.width * root.safeSyllableProgress)
    
    // Texto base (efecto simple)
    Row {
        id: textRow
        anchors.centerIn: parent
        spacing: 0
        
        // Texto cantado
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }
        
        // Sílaba actual con recorte progresivo
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height
            
            Text {
                id: currentSyllableText
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }
            
            Rectangle {
                width: parent.width * root.safeSyllableProgress
                height: parent.height
                color: "transparent"
                clip: true
                
                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                }
            }
        }
        
        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }
    
    // Pelota rebotante con tamaño adaptativo
    Item {
        id: ballContainer
        visible: root.safeCurrentSyllable.length > 0 && root.safeSyllableProgress > 0
        
        // Cálculos de física del rebote (exactos del original pero adaptativos)
        property real smoothProgress: Math.pow(root.safeSyllableProgress, ballSmoothness)
        property real bouncePhase: smoothProgress * Math.PI * 2
        property real bounceFactor: 0.5 * (1 + Math.sin(bouncePhase - Math.PI/2))
        
        // Posición horizontal de la pelota (basada en medidas reales)
        property real ballX: textRow.x + root.ballXPosition
        
        // Posición vertical de la pelota (adaptativa al tamaño de fuente)
        property real ballY: textRow.y - root.adaptiveBallOffset - root.adaptiveBounceHeight * bounceFactor
        
        // Velocidad vertical para efecto squash
        property real verticalVelocity: Math.cos(bouncePhase - Math.PI/2)
        property real squashFactor: 1.0 - ballSquash * Math.abs(verticalVelocity)
        property real stretchFactor: 1.0 / squashFactor
        
        // Sombra de la pelota (tamaño adaptativo)
        Rectangle {
            x: ballContainer.ballX - width/2
            y: textRow.y - root.adaptiveBallOffset/2 - height/2
            width: root.adaptiveBallSize * (1.0 + 0.3 * ballContainer.bounceFactor)
            height: width/4
            radius: width/2
            color: Qt.rgba(0, 0, 0, shadowAlpha/255 * (1.0 - 0.3 * ballContainer.bounceFactor))
            antialiasing: true
        }
        
        // Pelota principal con gradiente (tamaño adaptativo)
        Rectangle {
            id: ball
            x: ballContainer.ballX - width/2
            y: ballContainer.ballY - height/2
            width: root.adaptiveBallSize
            height: root.adaptiveBallSize
            radius: root.adaptiveBallSize/2
            antialiasing: true
            
            transform: Scale {
                xScale: ballContainer.stretchFactor
                yScale: ballContainer.squashFactor
                origin.x: ball.width/2
                origin.y: ball.height/2
            }
            
            gradient: Gradient {
                GradientStop {
                    position: 0.0
                    color: Qt.lighter(root.ballColor, 1.3)
                }
                GradientStop {
                    position: 0.7
                    color: root.ballColor
                }
                GradientStop {
                    position: 1.0
                    color: Qt.darker(root.ballColor, 1.2)
                }
            }
            
            // Brillo de la pelota (tamaño adaptativo)
            Rectangle {
                x: -root.adaptiveBallSize * 0.2 - width/2
                y: -root.adaptiveBallSize * 0.2 - height/2
                width: root.adaptiveBallSize * highlightSize
                height: width
                radius: width/2
                antialiasing: true
                
                gradient: Gradient {
                    GradientStop { position: 0.0; color: Qt.rgba(1, 1, 1, 0.7) }
                    GradientStop { position: 1.0; color: Qt.rgba(1, 1, 1, 0) }
                }
            }
        }
    }
    
    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }
    
    // Debug info adaptativo
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 220
        height: 100
        color: "black"
        border.color: "orange"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode
        
        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2
            
            Text {
                text: "Font Size: " + root.textFont.pixelSize + "px"
                color: "orange"
                font.pixelSize: 9
            }
            Text {
                text: "Ball Size: " + root.adaptiveBallSize.toFixed(1) + "px"
                color: "orange"
                font.pixelSize: 9
            }
            Text {
                text: "Ball Offset: " + root.adaptiveBallOffset.toFixed(1) + "px"
                color: "orange"
                font.pixelSize: 9
            }
            Text {
                text: "Ball X: " + root.ballXPosition.toFixed(1) + "px"
                color: "orange"
                font.pixelSize: 9
            }
            Text {
                text: "Bounce Height: " + root.adaptiveBounceHeight.toFixed(1) + "px"
                color: "orange"
                font.pixelSize: 9
            }
        }
    }
}
