import QtQuick 2.15
import QtQuick.Particles 2.15
import "../components"

EffectBase {
    id: root
    effectName: "ParticleEffect"
    
    // Parámetros del efecto particle (adaptables al tamaño del texto)
    property int particleCount: 15
    property real particleSpeed: 80
    property int particleLife: 800
    property int particleSizeMin: 2
    property int particleSizeMax: 5
    
    // Timer independiente para animaciones constantes
    property real animationTime: 0.0
    
    Timer {
        running: true
        repeat: true
        interval: 16 // 60 FPS
        onTriggered: {
            root.animationTime += 0.016
        }
    }
    
    // Propiedades seguras
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0
    
    // TextMetrics para cálculos precisos de tamaño
    TextMetrics {
        id: sungTextMetrics
        font: root.textFont
        text: root.safeSungText
    }
    
    TextMetrics {
        id: currentSyllableMetrics
        font: root.textFont
        text: root.safeCurrentSyllable
    }
    
    TextMetrics {
        id: fullTextMetrics
        font: root.textFont
        text: root.safeSungText + root.safeCurrentSyllable + root.safePendingText
    }
    
    // Cálculo preciso de la posición del progreso
    readonly property real progressPosition: sungTextMetrics.width + (currentSyllableMetrics.width * root.safeSyllableProgress)
    
    // Colores brillantes para partículas (mucho más brillantes que las letras)
    readonly property color ultraBrightParticle: Qt.rgba(1.0, 1.0, 1.0, 1.0)
    readonly property color superBrightGreen: Qt.rgba(0.5, 1.0, 0.5, 1.0)
    readonly property color brilliantCyan: Qt.rgba(0.5, 1.0, 1.0, 1.0)
    readonly property color glowingYellow: Qt.rgba(1.0, 1.0, 0.5, 1.0)
    
    // Texto base (efecto simple con recorte progresivo)
    Row {
        id: textRow
        anchors.centerIn: parent
        spacing: 0
        
        // Texto cantado
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }
        
        // Sílaba actual con recorte progresivo
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height
            
            Text {
                id: currentSyllableText
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }
            
            Rectangle {
                width: parent.width * root.safeSyllableProgress
                height: parent.height
                color: "transparent"
                clip: true
                
                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                }
            }
        }
        
        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }
    
    // Sistema de partículas nativo de QML con posición adaptativa
    ParticleSystem {
        id: particleSystem
        anchors.fill: parent
        running: root.safeCurrentSyllable.length > 0 && root.safeSyllableProgress > 0 && root.safeSyllableProgress < 1
        
        // Emisor de partículas con posición calculada dinámicamente
        Emitter {
            id: particleEmitter
            
            // Posición del emisor basada en medidas reales del texto
            x: textRow.x + root.progressPosition
            y: textRow.y + textRow.height/2
            
            // Configuración del emisor adaptada al tamaño de fuente
            emitRate: particleCount * 3
            lifeSpan: particleLife
            lifeSpanVariation: particleLife * 0.4
            
            // Tamaño de las partículas adaptado al tamaño de fuente
            size: Math.max(particleSizeMax * 1.5, root.textFont.pixelSize * 0.2)
            sizeVariation: Math.max(particleSizeMax - particleSizeMin, root.textFont.pixelSize * 0.1)
            endSize: Math.max(particleSizeMin * 1.2, root.textFont.pixelSize * 0.1)
            
            // Velocidad adaptada al tamaño de fuente
            velocity: AngleDirection {
                angle: 0
                angleVariation: 360
                magnitude: particleSpeed * (root.textFont.pixelSize / 24) // Escalar según tamaño de fuente
                magnitudeVariation: particleSpeed * 0.6 * (root.textFont.pixelSize / 24)
            }
            
            // Aceleración proporcional
            acceleration: AngleDirection {
                angle: 90
                magnitude: 25 * (root.textFont.pixelSize / 24)
            }
        }
        
        // Partículas visuales súper brillantes
        ImageParticle {
            id: particles
            
            // Colores súper brillantes que cambian constantemente
            color: {
                var phase = root.animationTime * 2.0
                var colorIndex = Math.floor(phase) % 4
                
                switch(colorIndex) {
                    case 0: return root.ultraBrightParticle
                    case 1: return root.superBrightGreen
                    case 2: return root.brilliantCyan
                    case 3: return root.glowingYellow
                    default: return root.ultraBrightParticle
                }
            }
            
            colorVariation: 0.4
            alpha: 1.0
            alphaVariation: 0.2
            
            // Rotación constante de las partículas
            rotation: root.animationTime * 45
            rotationVariation: 180
            rotationVelocity: 60 + 30 * Math.sin(root.animationTime * 3)
            rotationVelocityVariation: 120
            
            source: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cmFkaWFsR3JhZGllbnQgaWQ9ImciIGN4PSI1MCUiIGN5PSI1MCUiIHI9IjUwJSI+PHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmZmZmZjtzdG9wLW9wYWNpdHk6MSIgLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZmZmZmY7c3RvcC1vcGFjaXR5OjAuMyIgLz48L3JhZGlhbEdyYWRpZW50PjwvZGVmcz48Y2lyY2xlIGN4PSI1IiBjeT0iNSIgcj0iNSIgZmlsbD0idXJsKCNnKSIvPjwvc3ZnPg=="
            entryEffect: ImageParticle.Scale
        }
        
        // Turbulencia adaptada al tamaño
        Turbulence {
            anchors.fill: parent
            strength: (20 + 10 * Math.sin(root.animationTime * 2)) * (root.textFont.pixelSize / 24)
            enabled: true
        }
        
        // Gravedad adaptada al tamaño
        Gravity {
            anchors.fill: parent
            magnitude: (35 + 15 * Math.cos(root.animationTime * 1.5)) * (root.textFont.pixelSize / 24)
            angle: 90
        }
    }
    
    // Partículas adicionales manuales con posición adaptativa
    Repeater {
        model: root.safeCurrentSyllable.length > 0 && root.safeSyllableProgress > 0 && root.safeSyllableProgress < 1 ? 8 : 0
        
        Rectangle {
            property real particlePhase: root.animationTime * 2 + index * 0.5
            property real particleRadius: (20 + 15 * Math.sin(particlePhase)) * (root.textFont.pixelSize / 24)
            property real particleAngle: particlePhase * 60 + index * 45
            
            x: {
                var centerX = textRow.x + root.progressPosition
                return centerX + particleRadius * Math.cos(particleAngle * Math.PI / 180) - width/2
            }
            
            y: {
                var centerY = textRow.y + textRow.height/2
                return centerY + particleRadius * Math.sin(particleAngle * Math.PI / 180) - height/2
            }
            
            // Tamaño adaptado al tamaño de fuente
            width: (4 + 2 * Math.sin(particlePhase * 2)) * (root.textFont.pixelSize / 24)
            height: width
            radius: width/2
            
            // Colores súper brillantes que rotan
            color: {
                var colorPhase = particlePhase + index
                var brightness = 0.8 + 0.2 * Math.sin(colorPhase * 3)
                
                switch(index % 4) {
                    case 0: return Qt.rgba(brightness, brightness, brightness, 0.9)
                    case 1: return Qt.rgba(0.5, brightness, 0.5, 0.9)
                    case 2: return Qt.rgba(0.5, brightness, brightness, 0.9)
                    case 3: return Qt.rgba(brightness, brightness, 0.5, 0.9)
                    default: return Qt.rgba(brightness, brightness, brightness, 0.9)
                }
            }
            
            // Efecto de brillo adicional
            Rectangle {
                anchors.centerIn: parent
                width: parent.width * 0.6
                height: parent.height * 0.6
                radius: width/2
                color: Qt.rgba(1, 1, 1, 0.8)
            }
        }
    }
    
    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }
    
    // Debug info adaptativo
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 250
        height: 100
        color: "black"
        border.color: "yellow"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode
        
        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2
            
            Text {
                text: "Font Size: " + root.textFont.pixelSize + "px"
                color: "yellow"
                font.pixelSize: 9
            }
            Text {
                text: "Sung Width: " + sungTextMetrics.width.toFixed(1) + "px"
                color: "yellow"
                font.pixelSize: 9
            }
            Text {
                text: "Current Width: " + currentSyllableMetrics.width.toFixed(1) + "px"
                color: "yellow"
                font.pixelSize: 9
            }
            Text {
                text: "Progress Pos: " + root.progressPosition.toFixed(1) + "px"
                color: "yellow"
                font.pixelSize: 9
            }
            Text {
                text: "Particle Scale: " + (root.textFont.pixelSize / 24).toFixed(2)
                color: "yellow"
                font.pixelSize: 9
            }
        }
    }
}
