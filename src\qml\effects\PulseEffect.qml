import QtQuick 2.15
import "../components"

EffectBase {
    id: root
    effectName: "PulseEffect"
    
    // Parámetros exactos del QPainter original
    property real pulseMinScale: 0.85
    property real pulseMaxScale: 1.25
    property real pulseSpeed: 3.0
    
    // Propiedades seguras
    readonly property string safeSungText: (root.isValid() && root.sungText) ? root.sungText : ""
    readonly property string safeCurrentSyllable: (root.isValid() && root.currentSyllable) ? root.currentSyllable : ""
    readonly property string safePendingText: (root.isValid() && root.pendingText) ? root.pendingText : ""
    readonly property string safeNextLineText: (root.isValid() && root.nextLineText) ? root.nextLineText : ""
    readonly property real safeSyllableProgress: (root.isValid() && typeof root.syllableProgress === "number") ? Math.max(0, Math.min(1, root.syllableProgress)) : 0.0
    
    Row {
        id: mainRow
        anchors.centerIn: parent
        spacing: 0
        
        // Texto cantado (sin efectos)
        Text {
            text: root.safeSungText
            font: root.textFont
            color: root.sungColor
            antialiasing: true
        }
        
        // Sílaba actual con efecto pulse (EXACTO al original QPainter)
        Item {
            width: currentSyllableText.width
            height: currentSyllableText.height
            
            // Texto de fondo (color pendiente) - siempre visible como base
            Text {
                id: currentSyllableText
                text: root.safeCurrentSyllable
                font: root.textFont
                color: root.pendingColor
                antialiasing: true
            }
            
            // Contenedor para la sílaba con pulse (solo cuando hay progreso)
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress > 0.0 && root.safeSyllableProgress < 1.0
                
                // Fondo negro para borrar el texto base (como en QPainter)
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }
                
                // Sílaba con efecto pulse (LÓGICA EXACTA del QPainter original)
                Text {
                    id: pulsingSyllable
                    anchors.centerIn: parent
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    antialiasing: true
                    
                    // Cálculo EXACTO del pulse (igual que en QPainter)
                    property real pulsePhase: root.safeSyllableProgress * root.pulseSpeed * Math.PI
                    property real scaleFactor: root.pulseMinScale + (root.pulseMaxScale - root.pulseMinScale) * 0.5 * (1.0 + Math.sin(pulsePhase))
                    
                    scale: scaleFactor
                    transformOrigin: Item.Center
                    
                    // Color EXACTO del QPainter original
                    color: {
                        var brightness = 128 + 127 * ((scaleFactor - root.pulseMinScale) / (root.pulseMaxScale - root.pulseMinScale))
                        return Qt.rgba(0, brightness/255, 0, 1) // Verde con brillo variable
                    }
                    
                    // Transiciones suaves pero rápidas como el original
                    Behavior on scale {
                        NumberAnimation {
                            duration: 30
                            easing.type: Easing.InOutSine
                        }
                    }
                    
                    Behavior on color {
                        ColorAnimation {
                            duration: 30
                            easing.type: Easing.InOutSine
                        }
                    }
                }
            }
            
            // Sílaba completada (tamaño normal, color cantado)
            Item {
                anchors.fill: parent
                visible: root.safeSyllableProgress >= 1.0
                
                // Fondo negro para borrar el texto base
                Rectangle {
                    anchors.fill: parent
                    color: "black"
                }
                
                Text {
                    text: currentSyllableText.text
                    font: currentSyllableText.font
                    color: root.sungColor
                    antialiasing: true
                }
            }
        }
        
        // Texto pendiente
        Text {
            text: root.safePendingText
            font: root.textFont
            color: root.pendingColor
            antialiasing: true
        }
    }
    
    // Línea siguiente
    Text {
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottom: parent.bottom
        anchors.bottomMargin: 50
        text: root.safeNextLineText
        font: root.nextLineFont
        color: root.nextLineColor
        antialiasing: true
        visible: text.length > 0
    }
    
    // Debug info (solo visible en modo debug)
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 5
        width: 200
        height: 60
        color: "black"
        border.color: "green"
        border.width: 1
        opacity: 0.8
        visible: root.debugMode
        
        Column {
            anchors.fill: parent
            anchors.margins: 3
            spacing: 2
            
            Text {
                text: "Pulse Phase: " + (root.safeSyllableProgress * root.pulseSpeed * Math.PI).toFixed(2)
                color: "green"
                font.pixelSize: 9
            }
            Text {
                text: "Scale Factor: " + (root.pulseMinScale + (root.pulseMaxScale - root.pulseMinScale) * 0.5 * (1.0 + Math.sin(root.safeSyllableProgress * root.pulseSpeed * Math.PI))).toFixed(3)
                color: "green"
                font.pixelSize: 9
            }
            Text {
                text: "Progress: " + (root.safeSyllableProgress * 100).toFixed(1) + "%"
                color: "green"
                font.pixelSize: 9
            }
        }
    }
}
