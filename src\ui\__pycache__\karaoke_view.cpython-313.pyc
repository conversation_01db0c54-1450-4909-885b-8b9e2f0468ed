�

    E�h�  �                   �   � S SK r S SKrS SKJr  S SKJrJrJrJrJ	r	  S SK
JrJrJ
r
JrJrJrJrJrJr  S SKJr  S SKJr   " S S\5      rg)	�    N)�QWidget)�Qt�QTimer�QRect�QPointF�QPoint)	�QPainter�QColor�QFont�QFontMetrics�QLinearGradient�QRadialGradient�QPainterPath�QPen�QImage)�
KaraokeEngine)�Songc                   ��   ^ � \ rS rSrSrSrSrSrSrSr	Sr
S	rS
rSr
S%U 4S jjrS
\4S jrS\4S jrS\4S jrS rS&S jrS\4S jrS rS rS rS rS rS rS rS r S r!S  r"S! r#S%S" jr$S# r%S$r&U =r'$ )'�KaraokeView�   z3Widget para mostrar letras de karaoke con resaltador   �   �   �   �   �   �   �   �   c           	      �6  >� [         TU ]  U5        U R                  SS5        U R                  [        R
                  S5        U R
                  S5        U R                  [        R                  5        U R                  [        R                  5        [        5       U l
        [        SSS5      U l        [        SSS5      U l
        [        SSS5      U l        [        SSS5      U l        SU l        U R"                  U l        SU l        S	U l        S
U l        SU l        SU l        S
U l        SU l        SU l        SU l        SU l        SU l        SU l        SU l        SU l         SU l!        [        SSS5      U l"        [        SSS5      U l#        U R                  U l$        SU l%        SU l&        SU l'        SU l(        SU l)        SU l*        SU l+        SU l,        U R                  U l-        [        []        SU R                  R_                  5       S -
  5      []        SU R                  Ra                  5       S -
  5      []        SU R                  Rc                  5       S -
  5      U R                  Re                  5       5      U l3        SU l4        0 U l5        [        SSS5      [        SSS5      [        SSS5      [        SSS5      /U l6        S!U l7        SU l8        SU l9        S"U l:        S#U l;        S$U l<        S%U l=        SU l>        S&U l?        SU l@        [        SSS5      [        SSS5      [        SSS5      /U lA        [�        S'S([�        R�                  5      U lD        [�        S'S
5      U lE        S)U lF        S U lG        S U lH        S)U lI        [�        5       U lK        U R�                  R�                  S*5        U R�                  R�                  R�                  U R�                  5        U R�                  R�                  5         U R�                  S5        U R�                  S+5        g ),NiX  ��   Tzbackground-color: black;r   ��   �   g      �?r   �   �<   皙�����?g      �?r   �x   皙�����?g      @g      8@g�������?g�������?g      2@�   �d   g333333�?g      �?g      @皙�����?皙�����?g      .@g�������?g333333�?�2   �      �?r   gffffff�?�   r   g       @�Arial�   �        �
   F)S�super�__init__�setMinimumSize�setAttributer   �WA_StyledBackground�
setStyleSheet�WA_OpaquePaintEvent�WA_NoSystemBackgroundr   �enginer
   �
color_sung�
color_pending�color_next_line�
color_ball�show_transition_effect�
EFFECT_SIMPLE�current_effect�zoom_factor�shift_amount�	ball_size�ball_offset�ball_smoothness�ball_squash�ball_shadow_offset�ball_shadow_alpha�ball_highlight_size�ball_always_visible�wave_amplitude�wave_length�
wave_speed�wave_max_scale�wave_scale_width�inner_light_color�outer_light_color�
base_color�pulse_min_scale�pulse_max_scale�pulse_speed�typewriter_char_delay�typewriter_fade_in�aegisub_rotation_max�aegisub_scale_min�aegisub_scale_max�aegisub_color_sung�max�red�green�blue�alpha�aegisub_color_pending�aegisub_active_lightness�aegisub_active_color_cache�aegisub_colors�aegisub_blur_radius�aegisub_transform_speed�aegisub_animation_smoothness�aegisub_max_blur_steps�aegisub_bold_scale_factor�particle_count�particle_size_min�particle_size_max�particle_speed�
particle_life�particle_colorsr   �Bold�font_current�	font_next�
last_position�	last_line�	last_note�
last_progressr   �update_timer�setInterval�timeout�connect�update�start�setUpdatesEnabled�setAutoFillBackground)�self�parent�	__class__s     ��\   C:\Users\<USER>\Desktop\Bacup\Widget_def_versión_1\ultrastar_basics\src\ui\karaoke_view.pyr4   �KaraokeView.__init__   s�  �� �
���� � 	
���C��%����"�0�0�$�7����5�6� 	
���"�0�0�1����"�2�2�3� $�o��� !��C��+���#�C��c�2���%�c�3��4��� ��c�1�-��� '+��#�"�0�0��� ������ ������"������"#���!$���#&�� �#'�� � "���������"��� $��� "(��S�#�!6���!'��S�#�!6����/�/���  $���#������ &)��"�"%��� %)��!�!$���!$��� #'�/�/��� &,���4�%�%�)�)�+�b�0�1���4�%�%�+�+�-��2�3���4�%�%�*�*�,�r�1�2����$�$�&�	&
��"� ),��%� +-��'� 
�3��S�!��3��S�!��3��S�!��3��S�!�	
��� $'�� �'*��$� -0��)�&'��#�)-��&� !���!"���!"���!��� ����1�c�1���3��S�!��3��S�!� 
��� "�'�2�u�z�z�:����w��+��� !��������� ��� #�H������%�%�b�)����!�!�)�)�$�+�+�6������!� 	
���t�$��"�"�5�)�    �songc                 �Z   � U R                   R                  U5        U R                  5         g)u   Establece la canción actualN)r;   �set_songr}   )r�   r�   s     r�   r�   �KaraokeView.set_song�   s   � ������T�"����
r�   �effect_typec                 �p   � XR                   :�  a'  XR                  ::  a  Xl        U R                  5         ggg)z#Cambia el tipo de efecto de karaokeN)rA   �EFFECT_AEGISUBrB   r}   )r�   r�   s     r�   �
set_effect�KaraokeView.set_effect�   s1   � ��,�,�,��@S�@S�1S�"-���K�K�M� 2T�,r�   �returnc                 �&   � / SQnXR                      $ )z$Devuelve el nombre del efecto actual)	�Simple�Zoom�Particle�Ball�Shift�Wave�Pulse�
Typewriterz
Aegisub 3D)rB   )r�   �effect_namess     r�   �get_effect_name�KaraokeView.get_effect_name�   s   � �
�� �/�/�0�0r�   c           	      �  � US:X  aQ  X l         X l        U R                  R                  5         UU R	                  US5      U R	                  US5      /U l        O�US:X  a  X l        O�US:X  ay  X l        [        [        SUR                  5       S-
  5      [        SUR                  5       S-
  5      [        SUR                  5       S-
  5      UR                  5       5      U l        OUS:X  a  X l        OUS	:X  a  X l        U R#                  5         g
)z%Cambia uno de los colores del karaoke�sung�333333�?g333333�?�current�pendingr   r,   �ball�	next_lineN)r<   r]   re   �clear�_get_lighter_colorrq   �
color_currentr=   r
   r^   r_   r`   ra   rb   rc   r?   r>   r}   )r�   �
color_type�colors      r�   �	set_color�KaraokeView.set_color�   s�   � ����#�O�&+�#��+�+�1�1�3� ��'�'��s�3��'�'��s�3�$�D� �
 �9�
$�!&��
�9�
$�!&�� *0��A�u�y�y�{�R�'�(��A�u�{�{�}�r�)�*��A�u�z�z�|�b�(�)����
�	*�D�&� �6�
!�#�O�
�;�
&�#(� ����
r�   c                 �4  � UR                  5        SU 3nX0R                  ;   a  U R                  U   $ UR                  5       nUR                  5       nUR	                  5       n[        USU-
  U-  -   5      n[        USU-
  U-  -   5      n[        USU-
  U-  -   5      n	[
        XxX�R                  5       5      n
[        U R                  5      S:�  a7  U R                  R                  [        [        U R                  5      5      5        X�R                  U'   U
$ )uM   Genera un color más claro basado en el color base, con caché para optimizar�_r!   r)   )�rgbare   r_   r`   ra   �intr
   rb   �len�pop�next�iter)r�   rT   �lightness_factor�	cache_key�r�g�b�new_r�new_g�new_b�	new_colors              r�   r�   �KaraokeView._get_lighter_color�   s  � � "���(�)��+;�*<�=�	� �7�7�7��2�2�9�=�=� 
�N�N���������O�O��� �A��q��$4�4�4�5���A��q��$4�4�4�5���A��q��$4�4�4�5�� �5��0@�0@�0B�C�	� �t�.�.�/�#�5��+�+�/�/��T�$�:Y�:Y�5Z�0[�\�5>�'�'�	�2��r�   �current_timec                 �&  � [        XR                  -
  5      S:  a  gU R                  R                  nU R                  R                  nU R                  R
                  nXl        U R                  R
                  U5        X R                  R                  :g  nX0R                  R                  :g  nU R                  R                  bb  U R                  R                  U l        U R                  R                  U l        U R                  R
                  U l	        U R
                  5         gU(       ab  U R                  R                  U l        U R                  R                  U l        U R                  R
                  U l	        U R
                  5         gg)zActualiza el tiempo actualg����Mb`?N)
�absru   r;   �current_line�current_note�
note_progressr}   rv   rw   rx   )r�   r�   �old_line�old_note�old_progress�line_changed�note_changeds          r�   �update_time�KaraokeView.update_time�   s$  � �
 �|�0�0�0�1�E�9�� �;�;�+�+���;�;�+�+���{�{�0�0�� *�������<�(�  �;�;�#;�#;�;���;�;�#;�#;�;�� �;�;�#�#�/�!�[�[�5�5�D�N�!�[�[�5�5�D�N�!%���!:�!:�D�� 
�K�K�M�
�!�[�[�5�5�D�N�!�[�[�5�5�D�N�!%���!:�!:�D�� 
�K�K�M�
 r�   c                 �p
  � [        U 5      nUR                  [         R                  S5        UR                  [         R                  5        UR                  U R
                  5       [        R                  5        U R                  R                  (       d  gU R                  R                  5       u  p4nU R                  R                  5       nU R                  R                  5       nUR                  U R                  5        [!        U R                  5      nX4-   U-   n	UR#                  U	5      n
U R%                  5       U
-
  S-  nU R'                  5       S-  nUR#                  U5      n
UR#                  U5      n[)        X�U-  -   5      n[+        5       nUR-                  [)        U5      [)        U5      U R                  U	5        UR/                  [        R0                  5        UR3                  U R4                  5        UR7                  U5        U R8                  U R:                  :X  a  U R=                  UUX�X�U5        GO�U R8                  U R>                  :X  a>  U R=                  UUX�X�U5        U(       a   US:�  a  US:  a  U RA                  X+X�X�U5        GO)U R8                  U RB                  :X  a  U RE                  UUX�X�U5        GO�U R8                  U RF                  :X  a*  U R=                  UUX�X�U5        U RI                  X+X�X�5        GO�U R8                  U RJ                  :X  a>  U R=                  UUX�X�U5        U(       a   US:�  a  US:  a  U RM                  X+X�X�U5        GO\U R8                  U RN                  :X  aE  UR                  U R
                  5       [        R                  5        U RQ                  X+X�X�X4U5	        GO�U R8                  U RR                  :X  a>  U R=                  UUX�X�U5        U(       a   US:�  a  US:  a  U RU                  X+X�X�U5        GO�U R8                  U RV                  :X  Ga-  UR                  U R
                  5       [        R                  5        UR/                  [        R0                  5        UR3                  U RX                  5        [+        5       nUR-                  X�U R                  U5        UR7                  U5        U R[                  X+X�X�X4U5	        UR/                  [        R0                  5        UR3                  U R4                  5        [+        5       nUR-                  X�-   U-   X�R                  U5        UR7                  U5        O]U R8                  U R\                  :X  aC  UR                  U R
                  5       [        R                  5        U R_                  X+X�X�X4U5	        U(       a�  UR                  U R`                  5        [!        U R`                  5      R#                  U5      nU R%                  5       U-
  S-  nX�R'                  5       -   S-   n[+        5       nUR-                  [)        U5      [)        U5      U R`                  U5        UR/                  [        R0                  5        UR3                  U Rb                  5        UR7                  U5        gg)zEvento de pintadoTNr   r1   r-   �   )2r	   �
setRenderHint�TextAntialiasing�setCompositionMode�CompositionMode_Source�fillRect�rectr   �blackr;   r�   �get_highlighted_text�get_next_line_text�get_syllable_progress�setFontrs   r   �horizontalAdvance�width�heightr�   r   �addText�setPen�NoPen�setBrushr=   �drawPathrB   rA   �_draw_simple_effect�EFFECT_ZOOM�_draw_zoom_effect_overlay�EFFECT_PARTICLE�_draw_particle_effect�EFFECT_BALL�_draw_ball_effect�EFFECT_SHIFT�_draw_shift_effect_overlay�EFFECT_WAVE�_draw_wave_effect_overlay�EFFECT_PULSE�_draw_pulse_effect_overlay�EFFECT_TYPEWRITERr<   �_draw_typewriter_effect_overlayr�   �_draw_aegisub_effectrt   r>   )r�   �event�painter�	sung_text�current_syllable�pending_text�next_line_text�syllable_progress�font_metrics�	full_text�
line_width�x�y�
sung_width�
current_width�progress_width�	text_path�	sung_path�pending_path�next_line_width�x_next�y_next�next_line_paths                          r�   �
paintEvent�KaraokeView.paintEvent  s�  � � �4�.�� 	���h�7�7��>� 	�"�"�8�#B�#B�C� 	�������b�h�h�/� �{�{���� 59�K�K�4T�4T�4V�1�	�\����7�7�9�� �K�K�=�=�?�� 	����)�)�*�#�D�$5�$5�6�� �0�<�?�	� "�3�3�I�>�
�
�Z�Z�\�J�
&�!�+���K�K�M�A��� "�3�3�I�>�
�$�6�6�7G�H�
� �Z�;L�+L�M�N�� !�N�	����#�a�&�#�a�&�$�*;�*;�Y�G� 	���r�x�x� �����+�+�,�����#� ���$�"4�"4�4��$�$�W�i��z�Zk�l�
�
 �
 �D�$4�$4�
4��$�$�W�i��z�Zk�l��$5��$;�@Q�TW�@W��.�.�w�1�-�l|�}��
�
 �
 �D�$8�$8�
8��&�&�w�	�1��\m�n�
�
 �
 �D�$4�$4�
4��$�$�W�i��z�Zk�l��"�"�7�q�m�_�
�
 �
 �D�$5�$5�
5��$�$�W�i��z�Zk�l��$5��$;�@Q�TW�@W��/�/��A�=�m}�~��
�
 �
 �D�$4�$4�
4� 
���T�Y�Y�[�"�(�(�3��*�*�7�q�m�hq�  FR�  
S�
�
 �
 �D�$5�$5�
5��$�$�W�i��z�Zk�l��$5��$;�@Q�TW�@W��/�/��A�=�m}�~��
�
 �
 �D�$:�$:�
:� 
���T�Y�Y�[�"�(�(�3��N�N�2�8�8�$����T�_�_�-�$��I����a�D�$5�$5�y�A����Y�'��0�0��Q�M�nw�  LX�  
Y��N�N�2�8�8�$����T�/�/�0�'�>�L�� � ���-�!?��DU�DU�Wc�d����\�*�
�
 �
 �D�$7�$7�
7� 
���T�Y�Y�[�"�(�(�3��%�%�g�!��cl�  AM�  
N� ��O�O�D�N�N�+�*�4�>�>�:�L�L�^�\�O��j�j�l�_�4��9�F��,�,�.�.��3�F� *�^�N��"�"�3�v�;��F��T�^�^�^�\� 
�N�N�2�8�8�$����T�1�1�2����^�,� r�   c                 �  � [        XVU-  -   5      nUS:�  Ga�  UR                  5         [        [        U5      SX�R                  5       5      n	UR	                  U	5        UR                  U R                  5        UR                  U5        UR                  5         U R                  (       Ga  US:�  Ga  US:  Ga  Sn
[        [        X8-   U
S-  -
  5      SU
U R                  5       5      n[        UR                  5       SUR                  5       S5      nUR                  SU R                  5        UR                  SU R                  5        UR                  5         UR	                  U5        UR                  [         R"                  5        UR                  U5        UR                  U5        UR                  5         ggggg)z;Dibuja el efecto simple de resaltado de izquierda a derechar   r1   r-   r2   r   N)r�   �saver   r�   �setClipRectr�   r<   r�   �restorer@   r
   �left�right�
setColorAtr=   r�   r   r�   )
r�   r�   r�   r�   r�   r�   r�   r�   r�   �	clip_rect�transition_width�transition_rect�gradients
                r�   r�   �KaraokeView._draw_simple_effect�  s�  � ��Z�;L�+L�M�N���A���L�L�N��c�!�f�a�����G�I����	�*� 
���T�_�_�-����Y�'��O�O�� �*�*�*�/@�3�/F�K\�_b�Kb�#%� � #(���*�-=�a�-?�?�@��$��K�K�M�	#�� +�#�(�(�*��#�)�)�+��	�� �#�#�C����9��#�#�C��);�);�<� ���� �#�#�O�4� ���r�x�x�(�� � ��*�� � ��+� ���!�C Lc�/F�*� r�   c                 �h  � SU R                   S-
  SU-
  -  -   nUR                  5         [        5       n	X$-   US-  -   n
Un[        [	        X$-   5      S[	        U5      U R                  5       5      nUR
                  U[        R                  5        UR                  X�5        UR                  X�5        UR                  U
* U* 5        U	R                  [	        X$-   5      [	        U5      U R                  U5        UR                  U R                  5        UR                  U	5        UR!                  5         g)z:Dibuja el efecto de zoom como complemento al efecto simpler-   r   r   N)rC   r  r   r   r�   r�   r�   r   r�   �	translate�scaler�   rs   r�   r<   r�   r
  )
r�   r�   r�   r�   r�   r�   r�   r�   �zoom�
syllable_path�
syllable_x�
syllable_y�
syllable_rects
                r�   r�   �%KaraokeView._draw_zoom_effect_overlay�  s  � � �d�&�&��,��7H�1H�I�I�� 	���� %��
� �^�m�a�&7�7�
��
� �c�!�.�1�1�c�-�6H�$�+�+�-�X�
��������1� 	���*�1��
�
�d�!����:�+�
�{�3� 	���c�!�.�1�3�q�6�4�;L�;L�N^�_�������)�����'� 	���r�   c                 ��  � U	(       aP  [        [        X5-   5      S[        U5      U R                  5       5      nUR                  U[        R
                  5        US:�  a�  UR
                  5         [        [        U5      S[        U5      U R                  5       5      nUR                  U5        UR                  U R                  5        UR                  U5        UR                  5         U
(       a�  UR
                  5         [        [        X5-   U-   5      S[        U R                  5       5      U R                  5       5      nUR                  U5        UR                  U R                  5        UR                  U5        UR                  5         U	(       a�  SU R                  S-
  SU-
  -  -   n
UR
                  5         [        5       nX5-   US-  -   nUnUR!                  UU5        UR#                  X�5        UR!                  U* U* 5        UR%                  [        X5-   5      [        U5      U R&                  U	5        UR                  U R                  5        UR                  U5        UR                  5         gg)u-   Dibuja el efecto de zoom en la sílaba actualr   r-   r   N)r   r�   r�   r�   r   r�   r  r	  r�   r<   r�   r
  r�   r=   rC   r   r  r  r�   rs   )r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r  r  r  r  r  r  s                    r�   �_draw_zoom_effect�KaraokeView._draw_zoom_effect�  s�  � � �!�#�a�n�"5�q�#�m�:L�d�k�k�m�\�M����]�B�H�H�5� ��>��L�L�N��c�!�f�a��Z��$�+�+�-�H�I����	�*����T�_�_�-����Y�'��O�O�� ��L�L�N��c�!�.�=�"@�A�1�c�$�*�*�,�FW�Y]�Yd�Yd�Yf�g�I����	�*����T�/�/�0����Y�'��O�O�� � �$�*�*�S�0�S�;L�5L�M�M�D� 
�L�L�N� )�N�M� ��-�!�*;�;�J��J� 
���j�*�5��M�M�$�%����z�k�J�;�7� 
�!�!�#�a�n�"5�s�1�v�t�?P�?P�Rb�c����T�_�_�-����]�+� 
�O�O��5 r�   c           	      �  � [        XVU-  -   5      nUS:�  Ga:  UR                  5         [        [        U5      SX�R                  5       5      n	UR	                  U	5        UR                  U R                  5        UR                  U5        UR                  5         UR                  5         [        [        X8-   5      S[        U R                  5       U-
  U-
  5      U R                  5       5      n	UR	                  U	5        UR                  U R                  5        UR                  U5        UR                  5         X8-   n
Un[        U R                  SU-
  -  S-   5      n[        U5       GH�  n
[        R                  " [        R                   " SS5      5      n[        R"                  " SU R$                  S-  SU-
  -  5      nU
[        R&                  " U5      U-  -   nU[        R(                  " U5      U-  -   nU R*                  X�R$                  S-  -  U R*                  U R,                  -
  -  -
  n[/        U R,                  U5      nSX�R$                  S-  -  -
  n[/        SU5      nSX�R$                  S-  -  S-  -   n[1        S	U5      nU R3                  U R                  U5      nUR                  5         UR5                  U5        UR                  U5        UR7                  [8        R:                  5        UR=                  [        UUS-  -
  5      [        UUS-  -
  5      [        U5      [        U5      5        UR                  5         GM�     g
g
)u9   Dibuja el efecto de partículas que emergen de las letrasr   r-   r   ih  r�   r*   r�   �      �?r%   N)r�   r  r   r�   r	  r�   r<   r�   r
  r�   r=   rl   �range�math�radians�random�randint�uniformro   �cos�sinrn   rm   r^   �minr�   �
setOpacityr�   r   r�   �drawEllipse)r�   r�   r�   r�   r�   r�   r�   r�   r�   r  �
particle_x�
particle_yrl   �i�angle�distance�px�py�size�opacity�	lightness�particle_colors                         r�   r�   �!KaraokeView._draw_particle_effect+  s�  � ��Z�;L�+L�M�N���A���L�L�N��c�!�f�a�����G�I����	�*����T�_�_�-����Y�'��O�O�� 
�L�L�N��c�!�"4�5�q�#�d�j�j�l�Q�>N�Q_�>_�:`�bf�bm�bm�bo�p�I����	�*����T�/�/�0����Y�'��O�O�� �+�J��J� !��!4�!4��>O�8O�!P�ST�!T�U�N� �>�*�����V�^�^�A�s�%;�<��!�>�>�!�T�-@�-@�2�-E��O`�I`�-a�b��  �$�(�(�5�/�H�"<�<���$�(�(�5�/�H�"<�<�� �-�-��=P�=P�SU�=U�1V�[_�[q�[q�tx�  uK�  uK�  \K�  1L�  L���4�1�1�4�8�� ��-@�-@�2�-E�!F�G���c�7�+��  �8�/B�/B�R�/G�#H�C�"O�O�	���Y�/�	� "&�!8�!8����)�!T�� �����"�"�7�+�� � ��0����r�x�x�(��#�#�C��T�!�V��$4�c�"�t�A�v�+�6F��D�	�SV�W[�S\�]����!�? +�5 r�   c           	      �h  � X$-   XV-  -   n[         R                  " X`R                  5      nU[         R                  -  S-  n	Sn
SS[         R                  " U	[         R                  S-  -
  5      -   -  nX0R
                  -
  X�-  -
  n[         R                  " U	[         R                  S-  -
  5      n
SU R                  U
-  -
  nSU-  nUR                  5         U R                  SSU-  -   -  n[        U R                  SSU-  -
  -  5      n[        SSSU5      nX0R
                  S-  -
  nUR                  U5        UR                  [        R                   5        UR#                  [        UUS-  -
  5      [        UUS-  -
  5      [        U5      [        US-  5      5        UR%                  X|5        UR'                  X�5        [)        SSU R                  5      nU R*                  n[        [-        SUR/                  5       S-
  5      [-        SUR1                  5       S-
  5      [-        SUR3                  5       S-
  5      5      n[        [5        S	UR/                  5       S
-   5      [5        S	UR1                  5       S
-   5      [5        S	UR3                  5       S
-   5      5      nUR7                  SU5        UR7                  SU5        UR7                  SU5        UR                  U5        UR                  [        R                   5        UR#                  [        U R                  * S-  5      [        U R                  * S-  5      U R                  U R                  5        U R                  U R8                  -  nU R                  * S
-  n[)        UUU5      nUR7                  S[        S	S	S	S5      5        UR7                  S[        S	S	S	S5      5        UR                  U5        UR#                  [        UUS-  -
  5      [        UUS-  -
  5      [        U5      [        U5      5        UR;                  5         g)uK   Dibuja una bola que rebota sobre las sílabas (estilo UltraStar WorldParty)r   r,   r   r   r-   r   r   r   r!   �F   r1   �ffffff�?r+   r(   N)r"  �powrG   �pir(  rF   r'  rH   r  rE   r�   rJ   r
   r�   r�   r   r�   r+  r  r  r   r?   r^   r_   r`   ra   r)  r
  rK   r
  )r�   r�   r�   r�   r�   r�   r�   �ball_x�smooth_progress�bounce_phase�
bounce_height�
bounce_factor�ball_y�vertical_velocity�
squash_factor�stretch_factor�shadow_size�shadow_alpha�shadow_color�shadow_yr  rT   �darker_color�
lighter_color�highlight_size�highlight_offset�highlight_gradients                              r�   r�   �KaraokeView._draw_ball_effectj  s�  � � ��-�"C�C�� �(�(�#4�6J�6J�K�� '����0�1�4�� �
��q�4�8�8�L�4�7�7�1�9�,D�#E�E�F�
� �%�%�%�
�(E�E��
 !�H�H�\�D�G�G�A�I�%=�>��
 �d�.�.�1B�B�B�
��}�,�� 	���� �n�n��c�M�.A�(A�B���4�1�1�S�3��;N�5N�O�P���a��A�|�4�� �'�'��)�)�� 	����&����r�x�x� �������Q��&�'���;�q�=�(�)������A�
��		
� 	���&�)��
�
�n�4� #�1�a����8�� �_�_�
� ���:�>�>�#�b�(�)���:�#�#�%��*�+���:�?�?�$�r�)�*�
�� ���Z�^�^�%��*�+���Z�%�%�'�"�,�-���Z�_�_�&��+�,�
�
� 	���C��/����C��,����C��.� 	����"����r�x�x� ����������!�"�������!�"��N�N��N�N�		
� ���$�*B�*B�B�� �N�N�?�S�0�� -��.��
�� 	�%�%�c�6�#�s�C��+E�F��%�%�c�6�#�s�C��+C�D� 	���+�,����� �>�!�#3�3�4�� �>�!�#3�3�4�������		
� 	���r�   c
                 �.  � [        U R                  5      n
U
R                  U5      nU
R                  U5      nX�-  n
X�-   nUn[        5       n[        5       n[        5       n[	        U5       GH/  u  nnU
R                  U5      nUUS-  -   n[        UX.-   -
  5      nSnUU R                  S-  :  a<  SU R                  S-
  [        R                  " SUU R                  -  S-  -  5      -  -   nUR                  5         UR                  UU5        UR                  UU5        [        5       nUR                  [        U* S-  S5      U R                  U5        UR                  5       R!                  U5      nUR#                  U5        UR%                  5         UU-
  nGM2     U(       Ga�  ['        [)        X+-   5      S[)        U5      U R+                  5       5      nUR-                  U[.        R0                  5        [	        U5       GHu  u  nnU
R                  U5      nUUS-  -   nUS:�  a
  X�-
  U-
  U-  OSnUU:*  nU(       Ga  [        UX.-   -
  5      nSnUU R                  S-  :  a<  SU R                  S-
  [        R                  " SUU R                  -  S-  -  5      -  -   nUR                  5         UR                  UU5        UR                  UU5        [        5       nUR                  [        U* S-  S5      U R                  U5        UR                  5       R!                  U5      nUR#                  U5        UR%                  5         O&UR                  [        X�5      U R                  U5        UU-
  nGMx     U	(       a&  UR                  [        X�5      U R                  U	5        UR3                  [.        R4                  5        UR7                  U R8                  5        UR;                  U5        UR=                  5       (       Gd�  UR                  5         [        U5      nUR?                  U5        [A        SSSU5      nURC                  SU RD                  5        URC                  SU RF                  5        UR3                  [.        R4                  5        UR7                  U5        UR;                  U5        [        U5      n UR                  5       n!U!R                  SS5        U!R!                  U 5      n UR3                  [.        R4                  5        UR7                  U RD                  5        UR;                  U 5        UR%                  5         UR                  5         UR3                  [I        U RD                  S5      5        UR7                  [.        RJ                  5        UR;                  U5        UR%                  5         UR3                  [.        R4                  5        UR7                  U RL                  5        UR;                  U5        g	)
uN   Dibuja el efecto de deformación de letras sin movimiento ondulatorio verticalr   r-   r   g333333ӿr   r1   g\���(\�?r   N)'r   rs   r�   r   �	enumerater�   rQ   rP   r"  �expr  r  r  r�   r   �	transform�map�addPathr
  r   r�   r�   r�   r   r�   r�   r�   r�   r<   r�   �isEmpty�setClipPathr
   r
  rR   rS   r   �NoBrushr=   )"r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   �sung_text_width�current_syllable_width�progress_in_current_syllable�progress_px�	current_xr�   �active_syllable_pathr   �idx�char�
char_width�char_center�dist_from_front�scale_factor�	char_path�transformed_pathr  �
char_progress�
is_karaoke�	mask_pathr  �
inner_glowrS  s"                                     r�   r�   �%KaraokeView._draw_wave_effect_overlay�  s�  � � $�D�$5�$5�6�� '�8�8��C��!-�!?�!?�@P�!Q��'=�'Q�$� &�D�� �	� !�N�	�+�~��#�~�� #�9�-�I�C��%�7�7��=�J�#�j�1�n�4�K� "�+���"A�B�O� �L���!6�!6��!:�:�"�d�&9�&9�C�&?�4�8�8�D�Tc�fj�f{�f{�T{�  AB�  TB�  MB�  DC�  &C�   C�� 
�L�L�N����k�1�-��M�M�,��5� %��I����g�z�k�!�m�Q�7��9J�9J�D�Q�  '�0�0�2�6�6�y�A�����.�/��O�O����#�I�9 .�> �!�#�a�&9�":�A�s�CY�?Z�\`�\g�\g�\i�j�M����]�B�H�H�5� '�'7�8�	��T�)�;�;�D�A�
�'�*�q�.�8�� _u�wx�^x����!@�DZ� Z�~�
�*�.?�?�
��&)�+���*I�&J�O� $'�L�&��)>�)>��)B�B�'*�d�.A�.A�C�.G�4�8�8�TX�\k�nr�  oD�  oD�  ]D�  IJ�  \J�  UJ�  LK�  .K�  (K�� �L�L�N��%�%�k�1�5��M�M�,��=� !-��I��%�%�g�z�k�!�m�Q�&?��AR�AR�TX�Y� (/�'8�'8�':�'>�'>�y�'I�$�(�0�0�1A�B��O�O�%� !�(�(���)>��@Q�@Q�SW�X��Z�'�	�I 9�N �� � ���!6��8I�8I�<�X�
 	���r�x�x� �������)�����#� $�+�+�-�-�
 
�L�L�N�$�%9�:�I� 
���	�*� '�q�!�Q��2�H�����T�%;�%;�<�����T�%;�%;�<� 
�N�N�2�8�8�$����X�&����1�2� &�&:�;�J��)�)�+�I��O�O�D�$�'�"���z�2�J� 
�N�N�2�8�8�$����T�3�3�4����Z�(� 
�O�O�� 
�L�L�N��N�N�4�� 6� 6��<�=����R�Z�Z�(����1�2��O�O�� 	���r�x�x� �����+�+�,�����&r�   c                 ��  � U R                   SU-
  -  nUR                  5         [        [        X$-   5      S[        U5      U R	                  5       5      n	UR                  U	[        R                  5        [        5       n
U
R                  [        X$-   5      [        X8-
  5      U R                  U5        UR                  U R                  5        UR                  U
5        UR                  5         g)zDDibuja el efecto de desplazamiento como complemento al efecto simpler-   r   N)rD   r  r   r�   r�   r�   r   r�   r   r�   rs   r�   r<   r�   r
  )r�   r�   r�   r�   r�   r�   r�   r�   �shiftr  r  s              r�   r�   �&KaraokeView._draw_shift_effect_overlay�  s�   � � �!�!�S�+<�%<�=�� 	���� �c�!�.�1�1�c�-�6H�$�+�+�-�X�
��������1� %��
����c�!�.�1�3�q�y�>�4�CT�CT�Vf�g� 	������)�����'� 	���r�   c                 �p  � X`R                   -  [        R                  -  nU R                  U R                  U R                  -
  S-  S[        R
                  " U5      -   -  -   n	UR
                  5         [        [        X$-   5      S[        U5      U R                  5       5      n
UR                  U
[        R                  5        X$-   US-  -   nUnUR                  X�5        UR                  X�5        UR                  U* U* 5        [        5       n
U
R!                  [        X$-   5      [        U5      U R"                  U5        [        SSX�R                  -
  U R                  U R                  -
  -  -  -   5      n[%        SUS5      nUR'                  U5        UR)                  U
5        UR+                  5         g)z3Dibuja el efecto de pulso que hace latir las letrasr   r-   r   r   r"   �   N)rW   r"  r<  rU   rV   r(  r  r   r�   r�   r�   r   r�   r  r  r   r�   rs   r
   r�   r�   r
  )r�   r�   r�   r�   r�   r�   r�   r�   �pulse_phaserd  r  �
char_center_x�
char_center_yr  �
brightness�pulse_colors                   r�   r�   �&KaraokeView._draw_pulse_effect_overlay�  s�  � � (�*:�*:�:�T�W�W�D���+�+�t�/C�/C�d�FZ�FZ�/Z�^a�.a�eh�ko�ks�ks�t�  lA�  fA�  /B�  B�� 	���� �c�!�.�1�1�c�-�6H�$�+�+�-�X�
��������1� ����):�:�
��
� 	���-�7��
�
�l�1����=�.�=�.�9� %��
����c�!�.�1�3�q�6�4�;L�;L�N^�_� ��s�|�6J�6J�'J�t�Oc�Oc�fj�fz�fz�Oz�&{�|�|�}�
��Q�
�A�.������%�����'� 	���r�   c
                 ��  � [        U R                  5      n
U
R                  U5      n[        [	        X+-   5      S[	        U5      U R                  5       5      nUR
                  U[        R                  5        U(       d  g[        U5      n
[        U
[	        X�-  5      S-   5      nX+-   n[        U5       GHO  nUU   nU
R                  U5      nUS-   U
-  n[        SUUU R                  -
  -
  U R                  -  5      n[        SU5      nUR                  5         UR!                  U5        [#        5       nUR%                  [	        U5      [	        U5      U R                  U5        UUS-
  :H  nU(       a.  U R'                  U R(                  S5      nUR+                  U5        OUR+                  U R(                  5        UR-                  [        R.                  5        UR1                  U5        UR3                  5         UU-
  nGMR     X�:  Gaj  Un[        X�5       H�  nUU   nU
R                  U5      n[#        5       nUR%                  [	        U5      [	        U5      U R                  U5        UR                  5         UR!                  S5        UR-                  [        R.                  5        UR+                  U R4                  5        UR1                  U5        UR3                  5         UU-
  nM�     UnX:R7                  5       -
  nU
R                  5       n[8        R:                  " US-  5      S:�  nU(       a=  [        [	        U5      [	        U5      S	U5      nUR
                  UU R(                  5        ggg)
u?   Dibuja el efecto de máquina de escribir con aparición gradualr   Nr   r-   r1   r'   r   r2   r   )r   rs   r�   r   r�   r�   r�   r   r�   r�   r)  r!  rX   rY   r^   r  r*  r   r�   r�   r<   r�   r�   r�   r�   r
  r=   �ascentr"  r(  )r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   rY  r  �
char_count�
visible_charsr]  r.  r`  ra  rg  �char_opacityre  �is_active_char�active_color�	pending_x�cursor_x�cursor_y�
cursor_height�cursor_blink�cursor_rects                                 r�   r�   �+KaraokeView._draw_typewriter_effect_overlay�  s   � � $�D�$5�$5�6�� '�8�8��C�� �c�!�"5�6��3�}�;M�t�{�{�}�]�
��������1�  �� �)�*�
��J��J�,J�(K�a�(O�P�
� �'�	� �}�%�A�#�A�&�D�%�7�7��=�J� ��U�j�0�M��s�%6�-�$�Jd�Jd�:d�%e�im�  jA�  jA�  %A�  B�L��s�L�1�L� 
�L�L�N� 
���|�,� %��I����c�)�n�c�!�f�d�6G�6G��N�  �=�1�#4�4�N��#�6�6�t����L��� � ��.� � � ����1� 
�N�N�2�8�8�$����Y�'� 
�O�O�� 
��#�I�Q &�V �%�!�I� �=�5��'��*��)�;�;�D�A�
� )�N�	��!�!�#�i�.�#�a�&�$�:K�:K�T�R� �����"�"�3�'����r�x�x�(�� � ��!3�!3�4�� � ��+����!� �Z�'�	�# 6�* !�H��.�.�0�0�H�(�/�/�1�M� !�H�H�%6��%;�<�q�@�L��#�C��M�3�x�=�!�]�S��� � ��d�o�o�>� �A &r�   c
                 �	  � [        U R                  5      n
U
R                  U5      nU
R                  U5      nUn
[        R                  " X`R
                  5      nU(       Ga0  [
        U5       GH   u  nnU
R                  U5      n[        R                  " US-   5        [        R                  " U R                  * S-  U R                  S-  5      n[        R                  " U R                  * S-  U R                  S-  5      n[        R                  " U R                  * S-  U R                  S-  5      n[        R                  " SS5      n[        R                  " SS5      nU R                  nU R                  XUUUUUUUUS5        U
U-
  n
GM#     U(       Gay  U R                  U R                  U R                  5      n[        U R                  5      nUR!                  S5        [
        U5       GH  u  nnU
R                  U5      n[#        U5      S:�  a  U[#        U5      -  OSnUU:*  nU(       Ga�  [        R                  " US-   5        U[        R$                  -  U R&                  -  nS	S[        R(                  " U5      -   -  nU R                  U-  [        R                  " S
S5      -  nU R                  U-  [        R                  " S
S5      -  nU R                  U-  [        R                  " S
S5      -  nU[        R$                  -  S-  U R&                  -  nSSS[        R(                  " U5      -   -  S-  -   nU[        R                  " U R*                  U R,                  5      -  nU[        R                  " U R*                  U R,                  5      -  nSU R.                  S-
  SU-
  -  -   n U R                  XUUUUUUUU -  UU -  SU5        O/U R                  XUUU R0                  SSSSSSU R                  5        U
U-
  n
GM      U	(       as  UR3                  [4        R6                  5        UR9                  U R0                  5        [;        5       n!U!R=                  X�U R                  U	5        UR?                  U!5        g
g
)zTDibuja el efecto estilo Aegisub con transformaciones 3D inspirado en el archivo .assr   r   g
ףp=
�?g{�G�z�?r-   Tr   r)   r   r:  r   g333333�?N) r   rs   r�   r"  r;  ri   rQ  r$  �seedr&  rZ   r]   �_draw_aegisub_charr�   rd   r   �setBoldr�   r<  rh   r(  r[   r\   rk   rc   r�   r   r�   r�   r   r�   r�   )"r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   rY  rZ  r]  r>  r_  r`  ra  �
rotation_x�
rotation_y�
rotation_z�scale_x�scale_y�
char_colorr}  �	bold_fontrg  rh  �phase�rotation_factor�scale_phaserd  �
bold_scaler   s"                                     r�   r�   � KaraokeView._draw_aegisub_effect(  s1  � � $�D�$5�$5�6�� '�8�8��C��!-�!?�!?�@P�!Q�� �	� �(�(�#4�6W�6W�X�� �&�y�1�	��T�)�;�;�D�A�
�
 ���C�!�G�$� $�^�^�T�-F�-F�,F�q�,H�$�Jc�Jc�de�Je�f�
�#�^�^�T�-F�-F�,F�q�,H�$�Jc�Jc�de�Je�f�
�#�^�^�T�-F�-F�,F�q�,H�$�Jc�Jc�de�Je�f�
� !�.�.��t�4�� �.�.��t�4�� "�4�4�
� �'�'��A�t�Z�'1�:�z�'.���>�
 �Z�'�	�5 2�: ��2�2�4�3J�3J�D�Li�Li�j�L� �d�/�/�0�I����d�#� '�'7�8�	��T�)�;�;�D�A�
� BE�EU�AV�YZ�AZ��s�+;�'<�!<�`a�
�*�.?�?�
�� �K�K��c�	�*� ,�d�g�g�5��8T�8T�T�E�&)�Q����%��-@�&A�O�!%�!:�!:�_�!L�v�~�~�^a�cf�Og�!g�J�!%�!:�!:�_�!L�v�~�~�^a�cf�Og�!g�J�!%�!:�!:�_�!L�v�~�~�^a�cf�Og�!g�J� #2�D�G�G�";�a�"?�$�B^�B^�"^�K�#&���T�X�X�k�5J�1J�)K�a�)O�#O�L�*�V�^�^�D�<R�<R�TX�Tj�Tj�-k�k�G�*�V�^�^�D�<R�<R�TX�Tj�Tj�-k�k�G� "%��(F�(F��(L�QT�Wf�Qf�'g�!g�J� �+�+�G��4��+5�z�:�+2�Z�+?��:�AU�WZ�+4�6� �+�+�G��4��Ic�Ic�+,�a��C��c�4�CT�CT�V� �Z�'�	�Y 9�` ��N�N�2�8�8�$����T�7�7�8�'�>�L�� � ��t�/@�/@�,�O����\�*�
 r�   c
                 �  � [        U5      S:  a�  [        U5      S:  a�  [        U5      S:  a�  [        U	S-
  5      S:  a�  [        U
S-
  5      S:  a�  UR                  5         U(       a  UOU R                  n
UR                  U5        UR	                  [
        R                  5        UR                  U5        [        5       nUR                  X#X�5        UR                  U5        UR                  5         gUR                  5         U(       a  UOU R                  n
[        5       n[        U
5      R                  U5      nX/S-  -   nUnUR                  UU5        [        U5      S:�  a  UR                  U5        U	nU
n[        U5      S:�  a/  [         R"                  " [         R$                  " U5      5      nUU-  n[        U5      S:�  a/  [         R"                  " [         R$                  " U5      5      nUU-  nUR'                  UU5        UR                  U* U* 5        UR                  U5        UR                  X#X�5        U R(                  S:�  Ga&  [+        U R,                  S5      nSU-  n[.        R0                  " S5        [3        U5       H�  nUU-  [         R4                  -  S-  n[         R"                  " U5      U R(                  -  n[         R6                  " U5      U R(                  -  nUR                  5         UR                  UU5        UR                  U5        UR	                  [
        R                  5        UR                  U5        UR                  U5        UR                  5         M�     UR	                  [
        R                  5        UR                  U5        UR                  U5        UR                  5         g)	uP   Dibuja un carácter con transformaciones 3D estilo Aegisub (versión optimizada)g{�G�z�?r-   Nr   r   r   r+   �*   )r�   r  rs   r*  r�   r   r�   r�   r   r�   r�   r
  r   r�   r  �rotater"  r'  r#  r  rg   r)  rj   r$  r�  r!  r<  r(  )r�   r�   r�   r�   r`  r�   �rot_x�rot_y�rot_zr�  r�  r4  �font�	char_fontre  ra  rr  rs  �
final_scale_x�
final_scale_y�scale_y_factor�scale_x_factor�
blur_steps�blur_opacityr.  r/  �offset_x�offset_ys                               r�   r�  �KaraokeView._draw_aegisub_char�  s,  � � �u�:����U��d�!2�s�5�z�D�7H�S�QX�[^�Q^�M_�bf�Mf�kn�ov�y|�o|�k}�  AE�  lE��L�L�N� !%��$�*;�*;�I� 
���w�'� 
�N�N�2�8�8�$����U�#�$��I����a�I�4����Y�'��O�O��� 	���� !�D�d�&7�&7�	� !�N�	�!�)�,�>�>�t�D�
� ��N�*�
��
� 	���-��7� �u�:����N�N�5�!�  �
��
� �u�:���!�X�X�d�l�l�5�&9�:�N��^�+�M� �u�:���!�X�X�d�l�l�5�&9�:�N��^�+�M� 	�
�
�m�]�3� 	���=�.�=�.�9� 	���7�#� 	���!�	�0� �#�#�a�'��T�8�8�!�<�J���+�L� 
�K�K��O��:�&���Z��4�7�7�2�Q�6���8�8�E�?�T�-E�-E�E���8�8�E�?�T�-E�-E�E�� ���� �!�!�(�H�5� �"�"�<�0����r�x�x�(�� � ��'�� � ��+� ���!�' '�, 	���r�x�x� ����������#� 	���r�   c                 �&  � U	(       aP  [        [        X5-   5      S[        U5      U R                  5       5      nUR                  U[        R
                  5        US:�  a�  UR
                  5         [        [        U5      S[        U5      U R                  5       5      nUR                  U5        UR                  U R                  5        UR                  U5        UR                  5         U
(       a�  UR
                  5         [        [        X5-   U-   5      S[        U R                  5       5      U R                  5       5      nUR                  U5        UR                  U R                  5        UR                  U5        UR                  5         U	(       a}  U R                  SU-
  -  n
[        5       nUR!                  [        X5-   5      [        XM-
  5      U R"                  U	5        UR                  U R                  5        UR                  U5        gg)u@   Dibuja el efecto de desplazamiento vertical de la sílaba actualr   r-   N)r   r�   r�   r�   r   r�   r  r	  r�   r<   r�   r
  r�   r=   rD   r   r�   rs   )r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r  r  rm  r  s                  r�   �_draw_shift_effect�KaraokeView._draw_shift_effect  s�  � � �!�#�a�n�"5�q�#�m�:L�d�k�k�m�\�M����]�B�H�H�5� ��>��L�L�N��c�!�f�a��Z��$�+�+�-�H�I����	�*����T�_�_�-����Y�'��O�O�� ��L�L�N��c�!�.�=�"@�A�1�c�$�*�*�,�FW�Y]�Yd�Yd�Yf�g�I����	�*����T�/�/�0����Y�'��O�O�� � �%�%��/@�)@�A�E� )�N�M��!�!�#�a�n�"5�s�1�9�~�t�GX�GX�Zj�k� 
���T�_�_�-����]�+� r�   )9re   rd   ri   rg   rk   rc   r]   rf   rj   rZ   r\   r[   rh   rL   rK   rF   rJ   rI   rE   rG   rH   rT   r?   r�   r>   r=   r<   rB   r;   rs   rt   rR   rv   rw   ru   rx   rS   rq   rl   rp   rn   rm   ro   rV   rU   rW   rD   r@   rX   rY   ry   rM   rN   rP   rQ   rO   rC   )N)r   )(�__name__�
__module__�__qualname__�__firstlineno__�__doc__rA   r�   r�   r�   r�   r�   r�   r�   r�   r4   r   r�   r�   r�   �strr�   r�   r�   �floatr�   r  r�   r�   r  r�   r�   r�   r�   r�   r�   r�   r�  r�  �__static_attributes__�
__classcell__)r�   s   @r�   r   r      s�   �� �=� �M��K��O��K��L��K��L����N�A*�F�T� �
�c� �1�� 1��B �D&�� &�PA-�F2"�h�B5�n="�~v�p]'�~�0$�Le?�Rn+�`l�\&,� &,r�   r   )r"  r$  �PyQt5.QtWidgetsr   �PyQt5.QtCorer   r   r   r   r   �PyQt5.QtGuir	   r
   r   r   r
   r   r   r   r   �src.core.karaoke_enginer   �src.core.song_loaderr   r   � r�   r�   �<module>r�     s3   �� � 
� #� ;� ;� {� {� {� 1� %�^,�'� ^,r�   
