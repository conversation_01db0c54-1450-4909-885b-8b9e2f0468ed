#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Tuple


@dataclass
class Note:
    """Represents an individual note in a song"""
    type: str  # ':' (normal), '*' (golden), 'F' (freestyle), 'R' (rap), 'G' (golden rap)
    start: int  # Start time in beats
    length: int  # Duration in beats
    pitch: int  # Pitch
    text: str  # Note text (syllable)


@dataclass
class Line:
    """Represents a lyrics line in a song"""
    start: int  # Start time in beats
    notes: List[Note] = field(default_factory=list)
    end: int = 0  # End time in beats (calculated)

    def get_text(self) -> str:
        """Return complete text of the line"""
        return ''.join(note.text for note in self.notes)


@dataclass
class Song:
    """Represents a complete song with metadata and notes"""
    title: str = ""
    artist: str = ""
    mp3_file: str = ""
    bpm: float = 0.0
    gap: float = 0.0
    cover: str = ""
    background: str = ""
    video: str = ""
    genre: str = ""
    edition: str = ""
    language: str = ""
    year: int = 0

    lines: List[Line] = field(default_factory=list)

    # Path to song file
    file_path: str = ""
    folder_path: str = ""

    def get_full_mp3_path(self) -> str:
        """Return complete path to MP3 file"""
        mp3_path = os.path.join(self.folder_path, self.mp3_file)

        # Check if file exists
        if os.path.exists(mp3_path):
            return mp3_path

        # If it doesn't exist, search for MP3 files in folder
        try:
            mp3_files = [f for f in os.listdir(self.folder_path)
                        if f.lower().endswith('.mp3')]

            if mp3_files:
                # Update MP3 file name
                self.mp3_file = mp3_files[0]
                return os.path.join(self.folder_path, self.mp3_file)
        except Exception:
            pass

        # If no MP3 file found, return original path
        return mp3_path

    def get_full_cover_path(self) -> str:
        """Return complete path to cover image"""
        if not self.cover:
            # Search for images in folder that could be covers
            try:
                image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
                for ext in image_extensions:
                    potential_covers = [f for f in os.listdir(self.folder_path)
                                      if f.lower().endswith(ext) and
                                      ('cover' in f.lower() or 'front' in f.lower() or 'portada' in f.lower())]
                    if potential_covers:
                        self.cover = potential_covers[0]
                        break

                # If no specific covers found, use any image
                if not self.cover:
                    for ext in image_extensions:
                        images = [f for f in os.listdir(self.folder_path) if f.lower().endswith(ext)]
                        if images:
                            self.cover = images[0]
                            break
            except Exception:
                pass

            if not self.cover:
                return ""

        cover_path = os.path.join(self.folder_path, self.cover)

        # Check if file exists
        if os.path.exists(cover_path):
            return cover_path

        # If it doesn't exist, return empty string
        return ""

    def get_gap_seconds(self) -> float:
        """Return GAP in seconds"""
        # Ensure GAP is a valid value
        if self.gap < 0:
            return 0.0

        # Convert from milliseconds to seconds
        return self.gap / 1000.0

    def get_beat_time(self, beat: int) -> float:
        """Convert a beat to time (seconds)"""
        if self.bpm <= 0:
            return 0
        return (beat * 60 / self.bpm) + self.get_gap_seconds()

    def get_current_line_and_note(self, current_time: float) -> Tuple[Optional[Line], Optional[Note], float]:
        """
        Get current line and note based on current time
        Returns (line, note, note_progress)
        """
        # Check if we're before initial GAP
        gap_seconds = self.get_gap_seconds()
        if current_time < gap_seconds:
            return None, None, 0.0

        # Calculate current beat with precision
        current_beat = (current_time - gap_seconds) * self.bpm / 60

        # Find current line
        current_line = None
        for line in self.lines:
            if current_beat >= line.start and current_beat <= line.end:
                current_line = line
                break
            elif current_beat < line.start:
                # Lines are ordered, so if we find one that hasn't started yet,
                # we can stop searching
                break

        if not current_line:
            return None, None, 0.0

        # Find current note
        current_note = None
        for i, note in enumerate(current_line.notes):
            note_end = note.start + note.length

            if current_beat >= note.start and current_beat < note_end:
                current_note = note

                # Calculate progress within note (0.0 - 1.0)
                # Use smooth function for progress
                raw_progress = (current_beat - note.start) / note.length

                # Apply smoothing function to make transition more gradual
                # but maintaining synchronization with audio
                # Use modified cubic ease-in-out function
                # that stays closer to real progress in the middle

                # Calculate smoothing factor that depends on progress
                # Less smoothing in middle to maintain synchronization
                smoothing_factor = 0.5  # 0.0 = no smoothing, 1.0 = full smoothing

                # Apply adaptive smoothing that ensures continuous progress
                if raw_progress < 0.2:
                    # At start: more smoothing (smooth start)
                    # Use quadratic function for smoother start
                    t = raw_progress / 0.2
                    smoothed_progress = raw_progress * (1.0 - smoothing_factor * (1.0 - t))
                elif raw_progress > 0.8:
                    # At end: more smoothing (smooth braking)
                    # Use linear function to avoid abrupt braking
                    smoothed_progress = raw_progress
                else:
                    # In middle: no smoothing (maintain exact synchronization)
                    smoothed_progress = raw_progress

                # Ensure progress is between 0.0 and 1.0
                progress = max(0.0, min(1.0, smoothed_progress))

                return current_line, current_note, progress

        # If we didn't find an active note but we're in a valid line,
        # determine if we're before first note or after last note
        if current_line.notes:
            first_note = current_line.notes[0]
            last_note = current_line.notes[-1]

            if current_beat < first_note.start:
                # We're before first note
                # If we're very close to first note, show it as selected with progress 0
                time_to_first = first_note.start - current_beat
                if time_to_first < 1.0:  # If we're less than 1 beat from first note
                    return current_line, first_note, 0.0
                else:
                    return current_line, None, 0.0
            elif current_beat >= last_note.start + last_note.length:
                # We're after last note
                # Keep last note fully highlighted
                return current_line, last_note, 1.0
            else:
                # We're between notes within the line
                # Find previous and next note
                for i in range(len(current_line.notes) - 1):
                    note = current_line.notes[i]
                    next_note = current_line.notes[i + 1]
                    note_end = note.start + note.length

                    if current_beat >= note_end and current_beat < next_note.start:
                        # We're between these two notes
                        # If we're closer to previous note, keep it highlighted
                        if current_beat - note_end < next_note.start - current_beat:
                            return current_line, note, 1.0
                        else:
                            # If we're closer to next note, show it as selected with progress 0
                            return current_line, next_note, 0.0

        return current_line, None, 0.0


class SongLoader:
    """Class for loading and parsing UltraStar song files"""

    @staticmethod
    def load_song(file_path: str) -> Optional[Song]:
        """Load a song from a .txt file"""
        if not os.path.exists(file_path):
            print(f"Error: File {file_path} does not exist")
            return None

        # List of encodings to try
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        content = None

        # Try different encodings
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                # File read successfully
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"Error reading file {file_path} with encoding {encoding}: {e}")

        # If file couldn't be read with any encoding
        if content is None:
            print(f"Error: Could not read file {file_path} with any known encoding")
            # Try reading in binary mode and decode ignoring errors
            try:
                with open(file_path, 'rb') as f:
                    content = f.read().decode('utf-8', errors='ignore')
                print(f"File read in binary mode with errors ignored")
            except Exception as e:
                print(f"Fatal error reading file {file_path}: {e}")
                return None

        # Create new song
        song = Song()
        song.file_path = file_path
        song.folder_path = os.path.dirname(file_path)

        # Split content into lines
        lines = content.splitlines()

        # Process headers
        i = 0
        while i < len(lines) and (not lines[i] or lines[i].startswith('#')):
            line = lines[i].strip()
            if line.startswith('#'):
                # Parse header - handle different formats
                # Some files may have spaces after # or before :
                header_match = re.match(r'#\s*([^:]+)\s*:(.*)', line)
                if header_match:
                    header_name = header_match.group(1).upper().strip()
                    header_value = header_match.group(2).strip()

                    if header_name == 'TITLE':
                        song.title = header_value
                    elif header_name == 'ARTIST':
                        song.artist = header_value
                    elif header_name == 'MP3':
                        song.mp3_file = header_value
                    elif header_name == 'BPM':
                        try:
                            # Clean value (remove spaces, non-numeric characters at start/end)
                            cleaned_value = re.sub(r'[^\d.,]', '', header_value.strip())

                            # Try converting using dot as decimal separator
                            try:
                                song.bpm = float(cleaned_value) * 4  # Multiply by 4 as in original code
                            except ValueError:
                                # If it fails, try replacing comma with dot
                                bpm_value = cleaned_value.replace(',', '.')
                                song.bpm = float(bpm_value) * 4

                            # BPM parsed successfully
                        except Exception as e:
                            print(f"Error parsing BPM: {header_value} - {str(e)}")
                    elif header_name == 'GAP':
                        try:
                            # Clean value (remove spaces, non-numeric characters at start/end)
                            cleaned_value = re.sub(r'[^\d.,\-]', '', header_value.strip())

                            # Try converting using dot as decimal separator
                            try:
                                gap_value = float(cleaned_value)
                            except ValueError:
                                # If it fails, try replacing comma with dot
                                gap_value = float(cleaned_value.replace(',', '.'))

                            # Check that GAP is a reasonable value
                            # Some files may have GAP in seconds instead of milliseconds
                            if gap_value > 0 and gap_value < 100:
                                # Probably in seconds, convert to milliseconds
                                gap_value *= 1000

                            song.gap = gap_value
                        except Exception as e:
                            print(f"Error parsing GAP: {header_value} - {str(e)}")
                    elif header_name == 'COVER':
                        song.cover = header_value
                    elif header_name == 'BACKGROUND':
                        song.background = header_value
                    elif header_name == 'VIDEO':
                        song.video = header_value
                    elif header_name == 'GENRE':
                        song.genre = header_value
                    elif header_name == 'EDITION':
                        song.edition = header_value
                    elif header_name == 'LANGUAGE':
                        song.language = header_value
                    elif header_name == 'YEAR':
                        try:
                            song.year = int(header_value)
                        except ValueError:
                            print(f"Error parsing YEAR: {header_value}")
            i += 1

        # Check required fields
        missing_fields = []

        # Try extracting information from file name if fields are missing
        file_name = os.path.basename(file_path)
        folder_name = os.path.basename(os.path.dirname(file_path))

        # Try extracting artist and title from folder name
        # Common format: "Artist - Title"
        folder_parts = folder_name.split(' - ', 1)

        if not song.title:
            # Try extracting title from folder name
            if len(folder_parts) > 1:
                song.title = folder_parts[1]
            else:
                # Try extracting from file name
                file_parts = os.path.splitext(file_name)[0].split(' - ', 1)
                if len(file_parts) > 1:
                    song.title = file_parts[1]
                else:
                    song.title = os.path.splitext(file_name)[0]
                    missing_fields.append("TITLE")

        if not song.artist:
            # Try extracting artist from folder name
            if len(folder_parts) > 0:
                song.artist = folder_parts[0]
            else:
                # Try extracting from file name
                file_parts = os.path.splitext(file_name)[0].split(' - ', 1)
                if len(file_parts) > 0:
                    song.artist = file_parts[0]
                else:
                    song.artist = "Unknown"
                    missing_fields.append("ARTIST")

        if not song.mp3_file:
            # Search for MP3 files in folder
            mp3_files = [f for f in os.listdir(os.path.dirname(file_path))
                        if f.lower().endswith('.mp3')]

            if mp3_files:
                song.mp3_file = mp3_files[0]
            else:
                # Try with file name + .mp3
                potential_mp3 = f"{os.path.splitext(file_name)[0]}.mp3"
                if os.path.exists(os.path.join(os.path.dirname(file_path), potential_mp3)):
                    song.mp3_file = potential_mp3
                else:
                    missing_fields.append("MP3")

        if song.bpm <= 0:
            # Search for BPM in file content
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    bpm_match = re.search(r'#\s*BPM\s*:\s*(\d+[.,]?\d*)', content, re.IGNORECASE)
                    if bpm_match:
                        bpm_value = bpm_match.group(1).replace(',', '.')
                        try:
                            song.bpm = float(bpm_value) * 4
                        except ValueError:
                            song.bpm = 240.0  # Default value
                            missing_fields.append("BPM")
                    else:
                        song.bpm = 240.0  # Default value
                        missing_fields.append("BPM")
            except Exception:
                song.bpm = 240.0  # Default value
                missing_fields.append("BPM")

        # Check if required fields are still missing
        if missing_fields:
            print(f"Warning: Missing fields in {file_path}: {', '.join(missing_fields)}")
            print(f"Using derived or default values")

        # If MP3 file is missing and we couldn't find one, we can't continue
        if not song.mp3_file:
            print(f"Error: Could not find MP3 file for {file_path}")
            return None

        # Check if GAP is valid
        if song.gap <= 0:
            # Try searching for GAP in file content
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    gap_match = re.search(r'#\s*GAP\s*:\s*(\d+[.,]?\d*)', content, re.IGNORECASE)
                    if gap_match:
                        gap_value = gap_match.group(1).replace(',', '.')
                        try:
                            gap_value = float(gap_value)
                            # Check if GAP is in seconds
                            if gap_value > 0 and gap_value < 100:
                                gap_value *= 1000  # Convert to milliseconds
                            song.gap = gap_value
                        except ValueError:
                            pass
            except Exception:
                pass

            # If we still don't have a valid GAP, set default value
            if song.gap <= 0:
                song.gap = 0

        # Process notes
        current_line = None

        while i < len(lines):
            line = lines[i].strip()
            i += 1

            if not line:
                continue

            # End of line
            if line.startswith('-'):
                parts = line.split()
                if len(parts) >= 2:
                    try:
                        # If there's a current line, set its end time
                        if current_line and song.lines:
                            song.lines[-1].end = int(parts[1])

                        # Create new line
                        current_line = Line(start=int(parts[1]))
                        song.lines.append(current_line)
                    except ValueError:
                        print(f"Error parsing line end: {line}")
                continue

            # Note
            if line[0] in [':', '*', 'F', 'R', 'G']:
                parts = line.split(' ', 3)
                if len(parts) >= 4:
                    try:
                        note_type = parts[0]
                        start = int(parts[1])
                        length = int(parts[2])
                        pitch = int(parts[3].split(' ')[0])
                        text = ' '.join(parts[3].split(' ')[1:])

                        # If there's no current line, create one
                        if not current_line:
                            current_line = Line(start=start)
                            song.lines.append(current_line)

                        # Add note to current line
                        note = Note(type=note_type, start=start, length=length, pitch=pitch, text=text)
                        current_line.notes.append(note)

                        # Update line end time
                        current_line.end = max(current_line.end, start + length)
                    except ValueError:
                        print(f"Error parsing note: {line}")
                continue

        # Check that there's at least one line
        if not song.lines:
            print(f"Error: No lines found in {file_path}")
            return None

        return song

    @staticmethod
    def load_songs_from_directory(directory: str) -> List[Song]:
        """Load all songs from a directory"""
        songs = []
        error_count = 0
        total_files = 0

        if not os.path.exists(directory):
            print(f"Error: Directory {directory} does not exist")
            return songs

        # Simple start message
        print(f"Loading songs from: {directory}...")

        # Walk through directory recursively
        for root, _, files in os.walk(directory):
            for file in files:
                if file.lower().endswith('.txt'):
                    total_files += 1
                    file_path = os.path.join(root, file)
                    song = SongLoader.load_song(file_path)
                    if song:
                        songs.append(song)
                    else:
                        error_count += 1

        # Show only concise summary
        print(f"Songs loaded: {len(songs)}/{total_files}")
        if error_count > 0:
            print(f"Songs with errors: {error_count}")

        return songs


if __name__ == "__main__":
    # Usage example
    import sys

    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        song = SongLoader.load_song(file_path)
        if song:
            print(f"Song: {song.artist} - {song.title}")
            print(f"BPM: {song.bpm}, GAP: {song.gap}")
            print(f"MP3 file: {song.get_full_mp3_path()}")
            print(f"Lines: {len(song.lines)}")

            # Show first line as example
            if song.lines:
                line = song.lines[0]
                print(f"First line: {line.get_text()}")
                print(f"Notes in first line: {len(line.notes)}")
    else:
        print("Usage: python song_loader.py <path_to_txt_file>")
