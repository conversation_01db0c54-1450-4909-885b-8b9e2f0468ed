#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import QUrl, QTimer, Signal, QObject
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput


class AudioPlayer(QObject):
    """Audio player class for PySide6"""

    # Signals
    positionChanged = Signal(float)  # Emitted when position changes (in seconds)
    stateChanged = Signal(int)  # Emitted when state changes
    durationChanged = Signal(float)  # Emitted when duration changes (in seconds)

    def __init__(self):
        super().__init__()

        # Create media player
        self.player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.player.setAudioOutput(self.audio_output)

        # Connect signals
        self.player.positionChanged.connect(self._on_position_changed)
        self.player.playbackStateChanged.connect(self._on_state_changed)
        self.player.durationChanged.connect(self._on_duration_changed)

        # Timer for more frequent position updates
        self.update_timer = QTimer()
        self.update_timer.setInterval(10)  # 100 FPS for smoother updates
        self.update_timer.timeout.connect(self._emit_position)

        # Interpolation for smooth position
        self.last_position_update = 0
        self.interpolation_enabled = True

        # State
        self.duration = 0.0
        self.position = 0.0

    def load(self, file_path: str):
        """Load an audio file"""
        # Stop current playback if any
        self.stop()

        # Reset variables
        self.position = 0.0
        self.duration = 0.0

        # Load the new file
        self.player.setSource(QUrl.fromLocalFile(file_path))

        # Emit initial signals
        self.positionChanged.emit(0.0)
        self.durationChanged.emit(0.0)

    def play(self):
        """Play the audio"""
        # Ensure position is 0 when starting playback
        self.player.setPosition(0)
        self.position = 0.0

        # Start playback
        self.player.play()
        self.update_timer.start()

        # Emit initial position
        self.positionChanged.emit(0.0)

    def pause(self):
        """Pause playback"""
        self.player.pause()
        self.update_timer.stop()

    def stop(self):
        """Stop playback"""
        self.player.stop()
        self.update_timer.stop()

    def set_position(self, position: float):
        """Set position in seconds"""
        self.player.setPosition(int(position * 1000))

    def get_position(self) -> float:
        """Get current position in seconds"""
        return self.position

    def get_duration(self) -> float:
        """Get duration in seconds"""
        return self.duration

    def is_playing(self) -> bool:
        """Check if audio is playing"""
        return self.player.playbackState() == QMediaPlayer.PlayingState

    def _on_position_changed(self, position: int):
        """Handler for position changes"""
        self.position = position / 1000.0
        self.positionChanged.emit(self.position)

    def _on_state_changed(self, state: int):
        """Handler for state changes"""
        self.stateChanged.emit(state)

        # Start or stop timer based on state
        if state == QMediaPlayer.PlayingState:
            self.update_timer.start()
        else:
            self.update_timer.stop()

    def _on_duration_changed(self, duration: int):
        """Handler for duration changes"""
        self.duration = duration / 1000.0
        self.durationChanged.emit(self.duration)

    def _emit_position(self):
        """Emit current position (called by timer) with smooth interpolation"""
        # Get actual position from player
        actual_position = self.player.position() / 1000.0

        if self.interpolation_enabled and self.is_playing():
            # Calculate elapsed time since last update
            current_time = self.update_timer.interval() / 1000.0

            # Calculate interpolated position
            interpolated_position = self.position + current_time

            # If difference between real and interpolated position is large,
            # use real position to avoid desynchronization
            if abs(actual_position - interpolated_position) > 0.1:
                self.position = actual_position
            else:
                self.position = interpolated_position

            # Emit signal with interpolated position
            self.positionChanged.emit(self.position)
        else:
            # If not using interpolation, simply use real position
            if actual_position != self.position:
                self.position = actual_position
                self.positionChanged.emit(self.position)
